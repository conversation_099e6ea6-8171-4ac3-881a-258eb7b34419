/**
 * 统一时间工具模块
 * 基于 dayjs 提供一致的时间处理API
 * 解决项目中时间处理分散和不一致的问题
 */

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 配置dayjs使用中文本地化
dayjs.locale('zh-cn')

/**
 * 获取当前时间字符串
 * @returns {string} HH:MM格式的时间字符串
 */
export function getCurrentTime() {
  return dayjs().format('HH:mm')
}

/**
 * 时间戳加毫秒数
 * @param {number} timestamp 基础时间戳
 * @param {number} milliseconds 要添加的毫秒数
 * @returns {number} 新的时间戳
 */
export function addMilliseconds(timestamp, milliseconds) {
  return dayjs(timestamp).add(milliseconds, 'millisecond').valueOf()
}

/**
 * 检查时间是否即将过期
 * @param {number} expiresAt 过期时间戳
 * @param {number} thresholdMinutes 阈值分钟数，默认30分钟
 * @returns {boolean} 是否即将过期
 */
export function isExpiringSoon(expiresAt, thresholdMinutes = 30) {
  if (!expiresAt) return true
  
  const now = dayjs()
  const expiryTime = dayjs(expiresAt)
  const thresholdTime = expiryTime.subtract(thresholdMinutes, 'minute')
  
  return now.isAfter(thresholdTime)
}

/**
 * 检查时间是否已过期
 * @param {number} expiresAt 过期时间戳
 * @returns {boolean} 是否已过期
 */
export function isExpired(expiresAt) {
  if (!expiresAt) return true
  
  const now = dayjs()
  const expiryTime = dayjs(expiresAt)
  
  return now.isAfter(expiryTime)
}

/**
 * 格式化时间戳
 * @param {number|string|Date} timestamp 时间戳
 * @param {string} format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
  return dayjs(timestamp).format(format)
}

/**
 * 时间戳比较函数
 * @param {number|string|Date} a 时间A
 * @param {number|string|Date} b 时间B
 * @returns {number} 比较结果：b比a新返回正数，相等返回0，a比b新返回负数
 */
export function compareTimestamps(a, b) {
  const timeA = dayjs(a)
  const timeB = dayjs(b)
  
  return timeB.valueOf() - timeA.valueOf()
}

/**
 * 格式化为本地化字符串
 * @param {number|string|Date} date 日期
 * @param {Object} options 格式选项
 * @returns {string} 本地化格式的时间字符串
 */
export function formatToLocaleString(date, options = {}) {
  const defaultOptions = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  }
  
  const finalOptions = { ...defaultOptions, ...options }
  
  // 构建dayjs格式字符串
  let format = ''
  if (finalOptions.year) format += 'YYYY-'
  if (finalOptions.month) format += 'MM-'
  if (finalOptions.day) format += 'DD '
  if (finalOptions.hour) format += 'HH:'
  if (finalOptions.minute) format += 'mm:'
  if (finalOptions.second) format += 'ss'
  
  // 清理格式字符串
  format = format.replace(/[-:\s]+$/, '')
  
  return dayjs(date).format(format)
}

/**
 * 格式化为本地化时间字符串（仅时间部分）
 * @param {number|string|Date} date 日期
 * @returns {string} HH:mm:ss格式的时间字符串
 */
export function formatToLocaleTimeString(date) {
  return dayjs(date).format('HH:mm:ss')
}

/**
 * 获取当前时间戳
 * @returns {number} 当前时间戳（毫秒）
 */
export function now() {
  return dayjs().valueOf()
}

/**
 * 创建dayjs实例
 * @param {number|string|Date} date 日期
 * @returns {dayjs.Dayjs} dayjs实例
 */
export function createDayjs(date) {
  return dayjs(date)
}

// 默认导出所有函数
export default {
  getCurrentTime,
  addMilliseconds,
  isExpiringSoon,
  isExpired,
  formatTimestamp,
  compareTimestamps,
  formatToLocaleString,
  formatToLocaleTimeString,
  now,
  createDayjs,
} 