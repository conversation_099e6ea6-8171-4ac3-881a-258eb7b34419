/**
 * 数学计算工具模块
 * 基于 number-precision 库提供精确的数学运算
 * 解决 JavaScript 浮点数计算精度问题
 */

import NP from 'number-precision'

// 启用边界检查（可根据需要关闭以提升性能）
NP.enableBoundaryChecking(true)

/**
 * 安全加法运算
 * @param {number} num1 第一个数
 * @param {number} num2 第二个数
 * @param {...number} nums 其他数字
 * @returns {number} 精确的加法结果
 */
export function safeAdd(num1, num2, ...nums) {
  if (nums.length === 0) {
    return NP.plus(num1, num2)
  }
  return NP.plus(num1, num2, ...nums)
}

/**
 * 安全减法运算
 * @param {number} num1 被减数
 * @param {number} num2 减数
 * @param {...number} nums 其他减数
 * @returns {number} 精确的减法结果
 */
export function safeMinus(num1, num2, ...nums) {
  if (nums.length === 0) {
    return NP.minus(num1, num2)
  }
  return NP.minus(num1, num2, ...nums)
}

/**
 * 安全乘法运算
 * @param {number} num1 第一个数
 * @param {number} num2 第二个数
 * @param {...number} nums 其他数字
 * @returns {number} 精确的乘法结果
 */
export function safeTimes(num1, num2, ...nums) {
  if (nums.length === 0) {
    return NP.times(num1, num2)
  }
  return NP.times(num1, num2, ...nums)
}

/**
 * 安全除法运算
 * @param {number} num1 被除数
 * @param {number} num2 除数
 * @param {...number} nums 其他除数
 * @returns {number} 精确的除法结果
 */
export function safeDivide(num1, num2, ...nums) {
  if (nums.length === 0) {
    return NP.divide(num1, num2)
  }
  return NP.divide(num1, num2, ...nums)
}

/**
 * 安全四舍五入
 * @param {number} num 要舍入的数字
 * @param {number} ratio 小数位数
 * @returns {number} 精确的舍入结果
 */
export function safeRound(num, ratio) {
  return NP.round(num, ratio)
}

/**
 * 计算茶水抽佣
 * @param {number} originalAmount 原始分数
 * @param {number} ratio 抽取比例（百分比，如5表示5%）
 * @param {number} currentBalance 当前茶水余额
 * @param {number|null} limitAmount 抽取上限金额
 * @returns {Object} { teaAmount: 抽取的茶水金额, actualAmount: 玩家实际得到的分数 }
 */
export function calculateTeaDeduction(originalAmount, ratio, currentBalance, limitAmount) {
  // 如果茶水比例为0或分数为负数，不抽取茶水
  if (ratio <= 0 || originalAmount <= 0) {
    return { teaAmount: 0, actualAmount: originalAmount }
  }
  
  // 计算抽取金额：originalAmount * ratio / 100
  let teaAmount = safeDivide(safeTimes(originalAmount, ratio), 100)
  
  // 如果设置了上限，检查是否超过剩余额度
  if (limitAmount !== null && limitAmount > 0) {
    const remainingLimit = safeMinus(limitAmount, currentBalance)
    if (remainingLimit <= 0) {
      // 已达到上限，不再抽取
      teaAmount = 0
    } else if (teaAmount > remainingLimit) {
      // 抽取金额超过剩余额度，只抽取剩余部分
      teaAmount = remainingLimit
    }
  }
  
  // 计算玩家实际得到的分数
  const actualAmount = safeMinus(originalAmount, teaAmount)
  
  return { teaAmount, actualAmount }
}

/**
 * 格式化金额显示
 * @param {number} amount 金额
 * @param {number} decimalPlaces 小数位数，默认2位
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmountDisplay(amount, decimalPlaces = 2) {
  const rounded = safeRound(amount, decimalPlaces)
  return rounded.toFixed(decimalPlaces)
}

/**
 * 更新余额
 * @param {number} currentBalance 当前余额
 * @param {number} changeAmount 变化金额（正数为增加，负数为减少）
 * @returns {number} 更新后的余额
 */
export function updateBalance(currentBalance, changeAmount) {
  return safeAdd(currentBalance, changeAmount)
}

/**
 * 简化大数字显示（保持精确计算）
 * @param {number} score 分数
 * @returns {string} 简化后的分数显示
 */
export function formatScoreDisplay(score) {
  const absScore = Math.abs(score) // 这里使用原生Math.abs是安全的，仅用于比较
  const prefix = score > 0 ? "+" : ""

  if (absScore >= 1000000) {
    const simplified = safeDivide(score, 1000000)
    return `${prefix}${formatAmountDisplay(simplified, 1)}M`
  } else if (absScore >= 10000) {
    const simplified = safeDivide(score, 10000)
    return `${prefix}${formatAmountDisplay(simplified, 1)}W`
  } else {
    return score > 0 ? `+${score}` : score.toString()
  }
} 