// 房间服务API - 真实的房间相关接口
// 调用云对象处理房间操作

import loginFun from './login.js';
import { showToast } from './commonMethods.js';

/**
 * 房间服务类
 * 封装所有房间相关的云对象调用
 */
export class RoomService {
  
  /**
   * 获取用户token
   * @returns {string|null} 用户token
   */
  static getUserToken() {
    try {
      return loginFun.storage.getToken();
    } catch (error) {
      console.error('获取用户token失败:', error);
      return null;
    }
  }

  /**
   * 统一的云对象调用方法
   * @param {string} method 方法名
   * @param {Array} params 参数数组
   * @returns {Promise<Object>} 响应结果
   */
  static async callCloudFunction(method, params = []) {
    try {
      const token = this.getUserToken();
      if (!token) {
        return {
          success: false,
          message: '用户未登录，请先登录'
        };
      }

      const roomObject = uniCloud.importObject('room');
      const result = await roomObject[method](token, ...params);
      
      // 统一处理响应格式
      if (result.code === 200) {
        return {
          success: true,
          data: result.data,
          message: result.message
        };
      } else {
        return {
          success: false,
          message: result.message || '操作失败',
          code: result.code
        };
      }
    } catch (error) {
      console.error(`调用云对象方法 ${method} 失败:`, error);
      return {
        success: false,
        message: '网络异常，请稍后重试'
      };
    }
  }

  /**
   * 玩家数据格式转换
   * 将云端的player数据转换为前端期望的格式
   * @param {Array} cloudPlayers 云端玩家数据
   * @returns {Array} 转换后的玩家数据
   */
  static transformPlayersData(cloudPlayers) {
    if (!Array.isArray(cloudPlayers)) {
      return [];
    }

    return cloudPlayers.map(player => ({
      id: player.user_id,
      name: player.nickname || '未知用户',
      avatarFileId: player.avatar_fileId || '',
      score: 0, // 初始分数为0
      isCreator: player.is_creator || false,
      joinTime: player.join_time
    }));
  }

  /**
   * 退出房间
   * @param {string} roomId 房间ID
   * @returns {Promise<Object>} 操作结果
   */
  static async leaveRoom(roomId) {
    try {
      if (!roomId) {
        return {
          success: false,
          message: '房间ID不能为空'
        };
      }

      console.log(`正在退出房间: ${roomId}`);
      
      const result = await this.callCloudFunction('leaveRoom', [roomId]);
      
      if (result.success) {
        console.log('退出房间成功:', result.data);
        
        // 检查房间是否自动结束
        if (result.data && result.data.room_auto_finished) {
          return {
            success: true,
            message: '退出房间成功，房间已自动结束',
            data: result.data
          };
        }
        
        return {
          success: true,
          message: '退出房间成功',
          data: result.data
        };
      } else {
        console.error('退出房间失败:', result.message);
        return result;
      }
    } catch (error) {
      console.error('退出房间异常:', error);
      return {
        success: false,
        message: '退出房间失败，请稍后重试'
      };
    }
  }

  /**
   * 获取房间信息
   * @param {string} roomId 房间ID
   * @param {boolean} isPreview 是否为预览模式（用于扫码/分享链接访问）
   * @returns {Promise<Object>} 房间信息
   */
  static async getRoomInfo(roomId, isPreview = false) {
    try {
      if (!roomId) {
        return {
          success: false,
          message: '房间ID不能为空'
        };
      }

      console.log(`正在获取房间信息: ${roomId}, 预览模式: ${isPreview}`);
      
      const result = await this.callCloudFunction('getRoomInfo', [roomId, isPreview]);
      
      if (result.success) {
        console.log('获取房间信息成功:', result.data);
        
        // 构建基础数据结构
        const baseData = {
          roomId: result.data.room_id,
          roomName: result.data.room_name,
          gameType: result.data.game_type,
          roomStatus: result.data.room_status,
          currentPlayers: result.data.current_players,
          maxPlayers: result.data.max_players,
          createTime: result.data.create_time,
          timeDisplay: result.data.timeDisplay,
          players: result.data.players,
        };

        // 预览模式返回基础信息
        if (result.data.isPreview) {
          return {
            success: true,
            data: {
              ...baseData,
              canJoin: result.data.canJoin,
              isPreview: true
            }
          };
        }

        // 完整模式返回所有信息
        return {
          success: true,
          data: {
            ...baseData,
            players: result.data.players,
            gameRounds: result.data.game_rounds,
            teaWaterLimitAmount: result.data.teaWaterLimitAmount,
            teaWaterRatio: result.data.teaWaterRatio,
            teaWaterBalance: result.data.teaWaterBalance
          }
        };
      } else {
        console.error('获取房间信息失败:', result.message);
        return result;
      }
    } catch (error) {
      console.error('获取房间信息异常:', error);
      return {
        success: false,
        message: '获取房间信息失败，请稍后重试'
      };
    }
  }

  /**
   * 获取房间玩家列表
   * @param {string} roomId 房间ID
   * @returns {Promise<Object>} 玩家列表
   */
  static async getRoomPlayers(roomId) {
    try {
      // 先获取房间信息
      const roomInfoResult = await this.getRoomInfo(roomId);
      
      if (!roomInfoResult.success) {
        return roomInfoResult;
      }

      const { players } = roomInfoResult.data;
      
      // 转换玩家数据格式
      const transformedPlayers = this.transformPlayersData(players);
      
      console.log('获取玩家列表成功，玩家数量:', transformedPlayers.length);
      
      return {
        success: true,
        data: transformedPlayers,
        message: '获取玩家列表成功'
      };
    } catch (error) {
      console.error('获取玩家列表异常:', error);
      return {
        success: false,
        message: '获取玩家列表失败，请稍后重试'
      };
    }
  }

  /**
   * 添加房间消息
   * @param {string} roomId 房间ID
   * @param {string} messageType 消息类型：score/system/settlement/distribute/collect/round_start/round_end
   * @param {Object} detailData 消息详细数据
   * @returns {Promise<Object>} 操作结果
   */
  static async addMessage(roomId, messageType, detailData) {
    try {
      if (!roomId || !messageType || !detailData) {
        return {
          success: false,
          message: '参数不完整'
        };
      }

      // 验证消息类型
      const validTypes = ['score', 'system', 'settlement', 'distribute', 'collect', 'round_start', 'round_end'];
      if (!validTypes.includes(messageType)) {
        return {
          success: false,
          message: `不支持的消息类型: ${messageType}`
        };
      }

      console.log(`正在添加房间消息: ${roomId}, 类型: ${messageType}`);
      
      const result = await this.callCloudFunction('addRoomMessage', [roomId, messageType, detailData]);
      
      if (result.success) {
        console.log('添加房间消息成功:', result.data);
      } else {
        console.error('添加房间消息失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('添加房间消息异常:', error);
      return {
        success: false,
        message: '添加消息失败，请稍后重试'
      };
    }
  }

  /**
   * 获取房间消息列表
   * @param {string} roomId 房间ID
   * @returns {Promise<Object>} 消息列表
   */
  static async getMessages(roomId) {
    try {
      if (!roomId) {
        return {
          success: false,
          message: '房间ID不能为空'
        };
      }

      console.log(`正在获取房间消息: ${roomId}`);
      
      const result = await this.callCloudFunction('getRoomMessages', [roomId]);
      
      if (result.success) {
        console.log('获取房间消息成功，消息数量:', result.data.messages.length);
      } else {
        console.error('获取房间消息失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('获取房间消息异常:', error);
      return {
        success: false,
        message: '获取消息失败，请稍后重试'
      };
    }
  }

  /**
   * 更新房间茶水设置
   * @param {string} roomId 房间ID
   * @param {Object} teaSettings 茶水设置 { teaWaterLimitAmount, teaWaterRatio }
   * @returns {Promise<Object>} 操作结果
   */
  static async updateRoomTeaSettings(roomId, teaSettings) {
    try {
      if (!roomId || !teaSettings) {
        return {
          success: false,
          message: '参数不完整'
        };
      }

      console.log(`正在更新房间茶水设置: ${roomId}`, teaSettings);
      
      const result = await this.callCloudFunction('updateRoomTeaSettings', [roomId, teaSettings]);
      
      if (result.success) {
        console.log('更新茶水设置成功:', result.data);
      } else {
        console.error('更新茶水设置失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('更新茶水设置异常:', error);
      return {
        success: false,
        message: '更新茶水设置失败，请稍后重试'
      };
    }
  }

  /**
   * 更新房间茶水余额
   * @param {string} roomId 房间ID
   * @param {number} teaAmountChange 茶水余额变化量
   * @returns {Promise<Object>} 操作结果
   */
  static async updateTeaWaterBalance(roomId, teaAmountChange) {
    try {
      if (!roomId || typeof teaAmountChange !== 'number') {
        return {
          success: false,
          message: '参数不完整或类型错误'
        };
      }

      console.log(`正在更新房间茶水余额: ${roomId}, 变化量: ${teaAmountChange}`);
      
      const result = await this.callCloudFunction('updateTeaWaterBalance', [roomId, teaAmountChange]);
      
      if (result.success) {
        console.log('更新茶水余额成功:', result.data);
      } else {
        console.error('更新茶水余额失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('更新茶水余额异常:', error);
      return {
        success: false,
        message: '更新茶水余额失败，请稍后重试'
      };
    }
  }
} 