// API配置
const CONFIG = {
  TOKEN_KEY: "suixin_scorer_token",
  TOKEN_EXPIRES_AT_KEY: "suixin_scorer_token_expires_at",
  USER_INFO_KEY: "suixin_scorer_user_info",
};

import { addMilliseconds, isExpiringSoon, isExpired, now } from "./dateUtils.js";

// 云对象实例
let userCloudObject = null;

/**
 * 初始化云对象
 */
function initCloudObject() {
  if (!userCloudObject) {
    userCloudObject = uniCloud.importObject("user");
  }
  return userCloudObject;
}

/**
 * 本地存储工具
 */
const storage = {
  // 获取token
  getToken() {
    return uni.getStorageSync(CONFIG.TOKEN_KEY) || null;
  },

  // 设置token
  setToken(token) {
    uni.setStorageSync(CONFIG.TOKEN_KEY, token);
  },

  // 清除token
  clearToken() {
    uni.removeStorageSync(CONFIG.TOKEN_KEY);
  },

  getTokenExpiresAt() {
    const expiresAt = uni.getStorageSync(CONFIG.TOKEN_EXPIRES_AT_KEY);
    return expiresAt ? parseInt(expiresAt, 10) : null;
  },

  setTokenExpiresAt(expiresInSeconds) {
    const expiresAt = addMilliseconds(now(), expiresInSeconds * 1000);
    uni.setStorageSync(CONFIG.TOKEN_EXPIRES_AT_KEY, expiresAt.toString());
  },

  clearTokenExpiresAt() {
    uni.removeStorageSync(CONFIG.TOKEN_EXPIRES_AT_KEY);
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = uni.getStorageSync(CONFIG.USER_INFO_KEY);
    return userInfo ? JSON.parse(userInfo) : null;
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    uni.setStorageSync(CONFIG.USER_INFO_KEY, JSON.stringify(userInfo));
  },

  // 清除用户信息
  clearUserInfo() {
    uni.removeStorageSync(CONFIG.USER_INFO_KEY);
  },

  // 清除所有数据
  clearAll() {
    this.clearToken();
    this.clearTokenExpiresAt();
    this.clearUserInfo();
  },
};

/**
 * 检查token是否即将过期
 */
function isTokenExpiringSoon() {
  const expiresAt = storage.getTokenExpiresAt();
  return isExpiringSoon(expiresAt, 30); // 30分钟阈值
}

/**
 * 微信登录
 */
async function login() {
  try {
    // 1. 获取微信登录code
    const loginResult = await new Promise((resolve, reject) => {
      uni.login({
        provider: "weixin",
        success: resolve,
        fail: reject,
      });
    });

    if (!loginResult.code) {
      throw new Error("获取微信登录code失败");
    }

    // 2. 调用云对象登录
    const cloudObject = initCloudObject();
    const result = await cloudObject.login(loginResult.code);

    if (result.code !== 200) {
      throw new Error(result.message || "登录失败");
    }

    // 3. 保存token和用户信息
    storage.setToken(result.data.token);
    storage.setTokenExpiresAt(result.data.expiresIn);
    storage.setUserInfo(result.data.userInfo);

    return {
      success: true,
      data: {
        token: result.data.token,
        userInfo: result.data.userInfo,
        isNewUser: result.data.isNewUser,
        expiresIn: result.data.expiresIn,
      },
    };
  } catch (error) {
    console.error("登录失败:", error);
    return {
      success: false,
      message: error.message || "登录失败",
    };
  }
}

/**
 * 刷新token
 */
async function refreshToken() {
  try {
    const currentToken = storage.getToken();
    if (!currentToken) {
      throw new Error("没有可刷新的token");
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.refreshToken(currentToken);

    if (result.code !== 200) {
      throw new Error(result.message || "Token刷新失败");
    }

    // 保存新token
    storage.setToken(result.data.token);
    storage.setTokenExpiresAt(result.data.expiresIn);

    return {
      success: true,
      token: result.data.token,
    };
  } catch (error) {
    console.error("Token刷新失败:", error);
    // 刷新失败时清除本地数据
    storage.clearAll();
    return {
      success: false,
      message: error.message || "Token刷新失败",
    };
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  try {
    let token = storage.getToken();

    if (!token) {
      throw new Error("用户未登录");
    }
    // 检查token是否即将过期，如果是则先刷新
    if (isTokenExpiringSoon()) {
      console.log("Token即将过期，尝试刷新...");
      const refreshResult = await refreshToken();
      if (refreshResult.success) {
        token = refreshResult.token;
      } else {
        throw new Error("Token已过期，请重新登录");
      }
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.getUserInfo(token);

    if (result.code !== 200) {
      if (result.code === 401) {
        // token无效，清除本地数据
        storage.clearAll();
        throw new Error("登录已过期，请重新登录");
      }
      throw new Error(result.message || "获取用户信息失败");
    }

    // 更新本地用户信息
    storage.setUserInfo(result.data);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return {
      success: false,
      message: error.message || "获取用户信息失败",
    };
  }
}

/**
 * 更新用户信息
 */
async function updateUserInfo(userInfo) {
  try {
    let token = storage.getToken();

    if (!token) {
      throw new Error("用户未登录");
    }

    // 检查token是否即将过期
    if (isTokenExpiringSoon()) {
      const refreshResult = await refreshToken();
      if (refreshResult.success) {
        token = refreshResult.token;
      } else {
        throw new Error("Token已过期，请重新登录");
      }
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.updateUserInfo(token, userInfo);

    if (result.code !== 200) {
      if (result.code === 401) {
        storage.clearAll();
        throw new Error("登录已过期，请重新登录");
      }
      throw new Error(result.message || "更新用户信息失败");
    }

    // 使用云对象返回的更新后的用户信息更新本地存储
    if (result.data) {
      storage.setUserInfo(result.data);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("更新用户信息失败:", error);
    return {
      success: false,
      message: error.message || "更新用户信息失败",
    };
  }
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
  const token = storage.getToken();
  const userInfo = storage.getUserInfo();

  if (!token || !userInfo) {
    return false;
  }

  // 检查token是否过期
  const expiresAt = storage.getTokenExpiresAt();
  return !isExpired(expiresAt);
}

/**
 * 退出登录
 */
function logout() {
  storage.clearAll();

  console.log("用户已退出登录");

  return {
    success: true,
    message: "退出登录成功",
  };
}

// 导出API
export default {
  // 登录相关
  login,
  logout,
  checkLoginStatus,

  // 用户信息相关
  getUserInfo,
  updateUserInfo,

  // Token相关
  refreshToken,
  isTokenExpiringSoon,

  // 存储相关
  storage,
};
