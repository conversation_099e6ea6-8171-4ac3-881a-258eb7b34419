/**
 * 公共工具方法集合
 * 用于消除项目中的重复代码
 */

import { formatScoreDisplay as preciseFormatScoreDisplay } from "./mathUtils.js";
import { getCurrentTime as getTimeFromDateUtils } from "./dateUtils.js";

/**
 * 统一的提示消息方法
 * @param {string} title - 提示标题
 * @param {string} icon - 图标类型
 */
export function showToast(title, icon = 'success') {
  uni.showToast({ title, icon });
}

/**
 * 获取当前时间字符串
 * @returns {string} HH:MM格式的时间字符串
 */
export function getCurrentTime() {
  return getTimeFromDateUtils();
}

/**
 * 统一错误处理方法
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @param {boolean} showToUser - 是否显示给用户
 */
export function handleError(error, defaultMessage = '操作失败', showToUser = true) {
  console.error('Error:', error);
  
  if (showToUser) {
    const message = error.message || defaultMessage;
    showToast(message, 'none');
  }
  
  return {
    success: false,
    error: error.message || defaultMessage
  };
}

/**
 * 验证用户信息是否有效
 * @param {Object} userInfo - 用户信息对象
 * @returns {boolean} 是否有效
 */
export function validateUserInfo(userInfo) {
  return userInfo && 
         userInfo._id && 
         userInfo.nickname && 
         userInfo.wx_openid;
}

/**
 * 格式化分数显示
 * @param {number} score - 分数
 * @returns {string} 格式化后的分数字符串
 */
export function formatScore(score) {
  return score > 0 ? `+${score}` : score.toString();
}

/**
 * 简化大数字显示
 * @param {number} score - 分数
 * @returns {string} 简化后的分数显示
 */
export function formatScoreDisplay(score) {
  // 使用精确计算的格式化函数
  return preciseFormatScoreDisplay(score);
} 