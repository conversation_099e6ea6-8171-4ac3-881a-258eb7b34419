/**
 * 结算工具模块
 * 负责处理个人结算计算逻辑
 */

import { safeAdd, safeMinus, formatAmountDisplay } from './mathUtils.js'

/**
 * 计算个人结算方案
 * @param {string} currentUserId 当前用户ID
 * @param {string} currentUserName 当前用户名称
 * @param {number} currentUserScore 当前用户分数
 * @param {Array} otherPlayers 其他玩家列表 [{id, name, score}]
 * @param {number} teaWaterBalance 茶水余额
 * @returns {Object} 结算结果 { canSettle, details, totalLabel, totalAmount, scoreAdjustments, newTeaBalance }
 */
export function calculatePersonalSettlement(currentUserId, currentUserName, currentUserScore, otherPlayers, teaWaterBalance) {
  // 如果当前用户分数为0，无需结算
  if (currentUserScore === 0) {
    return {
      canSettle: false,
      reason: '您的分数为0，无需结算'
    }
  }

  // 过滤出有分数的其他玩家
  const playersWithScore = otherPlayers.filter(player => player.score !== 0)

  // 检查是否可以结算：要么有其他玩家有分数，要么有茶水余额可以结算
  if (playersWithScore.length === 0 && teaWaterBalance === 0) {
    return {
      canSettle: false,
      reason: '其他玩家分数均为0且茶水余额为0，无需结算'
    }
  }

  const details = []
  const scoreAdjustments = [] // 记录分数调整信息
  let remainingUserScore = currentUserScore
  let remainingTeaBalance = teaWaterBalance

  if (currentUserScore > 0) {
    // 当前用户有正分，需要其他负分玩家向我付钱
    const debtors = playersWithScore
      .filter(player => player.score < 0)
      .sort((a, b) => a.score - b.score) // 按负分从多到少排序

    if (debtors.length === 0 && remainingTeaBalance === 0) {
      return {
        canSettle: false,
        reason: '没有负分玩家且茶水余额为0，无法结算'
      }
    }

    // 贪心算法：优先从负分最多的玩家收取
    for (const debtor of debtors) {
      if (remainingUserScore <= 0) break

      // 计算该玩家能付给我的最大金额
      const maxPayment = Math.min(Math.abs(debtor.score), remainingUserScore)
      
      if (maxPayment > 0) {
        details.push({
          from: debtor.name,
          to: currentUserName,
          amount: formatAmountDisplay(maxPayment)
        })

        // 记录分数调整
        scoreAdjustments.push({
          playerId: debtor.id,
          adjustment: maxPayment, // 债务人分数增加（减少负分）
          reason: `向${currentUserName}结算`
        })

        remainingUserScore = safeMinus(remainingUserScore, maxPayment)
      }
    }

    // 如果还有剩余正分且有茶水余额，从茶水中获取
    if (remainingUserScore > 0 && remainingTeaBalance > 0) {
      const teaPayment = Math.min(remainingTeaBalance, remainingUserScore)
      
      if (teaPayment > 0) {
        details.push({
          from: '茶水',
          to: currentUserName,
          amount: formatAmountDisplay(teaPayment)
        })

        // 记录茶水的分数调整
        scoreAdjustments.push({
          playerId: 'tea_water', // 特殊的茶水玩家ID
          adjustment: -teaPayment, // 茶水分数减少
          reason: `向${currentUserName}结算`
        })

        remainingUserScore = safeMinus(remainingUserScore, teaPayment)
        remainingTeaBalance = safeMinus(remainingTeaBalance, teaPayment)
      }
    }

  } else {
    // 当前用户有负分，需要向其他正分玩家付钱
    const creditors = playersWithScore
      .filter(player => player.score > 0)
      .sort((a, b) => b.score - a.score) // 按正分从多到少排序

    if (creditors.length === 0 && remainingTeaBalance === 0) {
      return {
        canSettle: false,
        reason: '没有正分玩家且茶水余额为0，无法结算'
      }
    }

    let remainingDebt = Math.abs(currentUserScore)

    // 先尝试用茶水余额抵扣债务
    if (remainingDebt > 0 && remainingTeaBalance > 0) {
      const teaPayment = Math.min(remainingTeaBalance, remainingDebt)
      
      if (teaPayment > 0) {
        details.push({
          from: currentUserName,
          to: '茶水',
          amount: formatAmountDisplay(teaPayment)
        })

        // 记录茶水的分数调整
        scoreAdjustments.push({
          playerId: 'tea_water', // 特殊的茶水玩家ID
          adjustment: -teaPayment, // 茶水分数减少（茶水支付给用户）
          reason: `从${currentUserName}收到结算`
        })

        remainingDebt = safeMinus(remainingDebt, teaPayment)
        remainingTeaBalance = safeMinus(remainingTeaBalance, teaPayment)
        remainingUserScore = safeAdd(remainingUserScore, teaPayment) // 负分减少
      }
    }

    // 贪心算法：优先向正分最多的玩家付钱
    for (const creditor of creditors) {
      if (remainingDebt <= 0) break

      // 计算我能付给该玩家的最大金额
      const maxPayment = Math.min(creditor.score, remainingDebt)
      
      if (maxPayment > 0) {
        details.push({
          from: currentUserName,
          to: creditor.name,
          amount: formatAmountDisplay(maxPayment)
        })

        // 记录分数调整
        scoreAdjustments.push({
          playerId: creditor.id,
          adjustment: -maxPayment, // 债权人分数减少
          reason: `从${currentUserName}收到结算`
        })

        remainingDebt = safeMinus(remainingDebt, maxPayment)
        remainingUserScore = safeAdd(remainingUserScore, maxPayment) // 负分减少
      }
    }
  }

  if (details.length === 0) {
    return {
      canSettle: false,
      reason: '无法找到合适的结算方案'
    }
  }

  // 计算净收支
  const netAmount = safeMinus(currentUserScore, remainingUserScore)
  const isNetIncome = netAmount > 0
  
  // 添加当前用户的分数调整信息
  const currentUserAdjustment = safeMinus(remainingUserScore, currentUserScore);
  if (currentUserAdjustment !== 0) {
    scoreAdjustments.push({
      playerId: currentUserId,
      adjustment: currentUserAdjustment,
      reason: '结算后分数重置'
    });
  }
  
  return {
    canSettle: true,
    details,
    totalLabel: isNetIncome ? '我的净收入' : '我的净支出',
    totalAmount: netAmount,
    scoreAdjustments,
    newTeaBalance: remainingTeaBalance,
    finalUserScore: remainingUserScore
  }
}

/**
 * 生成结算详情数据用于保存到数据库
 * @param {Object} settlementResult 结算计算结果
 * @returns {Object} 数据库存储格式
 */
export function generateSettlementDetails(settlementResult) {
  return {
    details: settlementResult.details,
    total_label: settlementResult.totalLabel,
    total_amount: settlementResult.totalAmount,
    // 保存所有分数调整信息，包括当前用户和茶水的
    score_adjustments: settlementResult.scoreAdjustments,
    // 保存茶水余额变化信息
    new_tea_balance: settlementResult.newTeaBalance
  }
}

/**
 * 格式化结算金额显示
 * @param {number} amount 金额
 * @returns {string} 格式化后的金额
 */
export function formatSettlementAmount(amount) {
  const prefix = amount > 0 ? '+' : ''
  return `${prefix}${formatAmountDisplay(amount)}`
}

/**
 * 从结算详情中解析玩家分数调整信息
 * @param {Object} settlementMessage 结算消息对象
 * @param {Array} players 玩家列表 [{id, name}]
 * @param {Object} context 上下文信息，包含茶水余额等
 * @returns {Object} 分数调整映射 { playerId: adjustment, tea_water: teaAdjustment }
 */
export function parseSettlementAdjustments(settlementMessage, players, context = {}) {
  const adjustments = {}
  
  // 使用保存的分数调整信息
  if (settlementMessage.score_adjustments && Array.isArray(settlementMessage.score_adjustments)) {
    console.log('解析结算分数调整信息');
    settlementMessage.score_adjustments.forEach(adjustment => {
      if (adjustment.playerId && typeof adjustment.adjustment === 'number') {
        adjustments[adjustment.playerId] = adjustment.adjustment;
        if (adjustment.playerId === 'tea_water') {
          console.log(`茶水分数调整: ${adjustment.adjustment} (${adjustment.reason})`);
        } else {
          console.log(`玩家${adjustment.playerId}分数调整: ${adjustment.adjustment} (${adjustment.reason})`);
        }
      }
    });
  } else {
    console.warn('结算消息中缺少分数调整信息');
  }
  
  return adjustments;
} 