{"bsonType": "object", "description": "WebSocket连接信息表，用于管理房间实时连接状态", "required": ["connection_id", "room_id", "user_id", "nickname"], "properties": {"_id": {"description": "连接记录文档ID，系统自动生成"}, "connection_id": {"bsonType": "string", "description": "WebSocket连接唯一标识", "minLength": 1, "maxLength": 100, "title": "连接ID"}, "room_id": {"bsonType": "string", "description": "房间ID，关联rooms表", "pattern": "^SX[0-9]{8}$", "title": "房间ID"}, "user_id": {"bsonType": "string", "description": "用户ID，关联users表", "minLength": 1, "title": "用户ID"}, "nickname": {"bsonType": "string", "description": "用户昵称", "minLength": 1, "maxLength": 50, "title": "用户昵称"}, "avatar_fileId": {"bsonType": ["string", "null"], "description": "用户头像云存储文件ID", "default": null, "title": "用户头像"}, "connect_time": {"bsonType": "timestamp", "description": "连接建立时间", "forceDefaultValue": {"$env": "now"}, "title": "连接时间"}, "last_heartbeat": {"bsonType": "timestamp", "description": "最后心跳时间", "forceDefaultValue": {"$env": "now"}, "title": "最后心跳时间"}, "status": {"bsonType": "string", "description": "连接状态：active活跃，expired过期", "enum": ["active", "expired"], "default": "active", "title": "连接状态"}, "client_info": {"bsonType": "object", "description": "客户端信息", "properties": {"platform": {"bsonType": ["string", "null"], "description": "客户端平台：web、app、mp-weixin等", "title": "客户端平台"}, "version": {"bsonType": ["string", "null"], "description": "客户端版本", "title": "客户端版本"}, "user_agent": {"bsonType": ["string", "null"], "description": "用户代理信息", "title": "用户代理"}}, "title": "客户端信息"}, "disconnect_time": {"bsonType": ["timestamp", "null"], "description": "断开连接时间", "default": null, "title": "断开时间"}, "disconnect_reason": {"bsonType": ["string", "null"], "description": "断开连接原因：normal正常断开，timeout超时，error错误", "enum": ["normal", "timeout", "error", null], "default": null, "title": "断开原因"}}, "permission": {"read": false, "create": false, "update": false, "delete": false, "count": false}, "index": [{"IndexName": "connection_id_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "connection_id", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "room_id_status_composite", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "room_id", "Direction": "1"}, {"Name": "status", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "user_id_room_id_composite", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}, {"Name": "room_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "last_heartbeat", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "last_heartbeat", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "connect_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "connect_time", "Direction": "-1"}], "MgoIsUnique": false}}, {"IndexName": "status_last_heartbeat_composite", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "status", "Direction": "1"}, {"Name": "last_heartbeat", "Direction": "1"}], "MgoIsUnique": false}}]}