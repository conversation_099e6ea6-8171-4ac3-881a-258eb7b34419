{"bsonType": "object", "description": "系统配置表，存储微信access_token等系统级配置", "required": ["config_key", "config_value"], "properties": {"_id": {"description": "配置ID，系统自动生成"}, "config_key": {"bsonType": "string", "description": "配置键名，如wx_access_token", "minLength": 1, "maxLength": 50, "title": "配置键"}, "config_value": {"bsonType": "string", "description": "配置值", "minLength": 1, "maxLength": 1000, "title": "配置值"}, "expires_at": {"bsonType": "timestamp", "description": "过期时间，对于有时效性的配置如access_token", "title": "过期时间"}, "update_time": {"bsonType": "timestamp", "description": "最后更新时间", "forceDefaultValue": {"$env": "now"}, "title": "更新时间"}, "description": {"bsonType": "string", "description": "配置说明", "maxLength": 200, "title": "配置说明"}}, "permission": {"read": false, "create": false, "update": false, "delete": false, "count": false}, "index": [{"IndexName": "config_key_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "config_key", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "expires_at", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "expires_at", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "update_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "update_time", "Direction": "-1"}], "MgoIsUnique": false}}]}