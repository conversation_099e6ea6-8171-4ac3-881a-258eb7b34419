{"bsonType": "object", "description": "用户房间参与记录表", "required": ["user_id", "room_id"], "properties": {"_id": {"description": "记录ID，系统自动生成"}, "user_id": {"bsonType": "string", "description": "用户ID，关联users表", "title": "用户ID"}, "room_id": {"bsonType": "string", "description": "房间ID，格式：SX+8位数字", "pattern": "^SX[0-9]{8}$", "title": "房间ID"}, "first_transaction_time": {"bsonType": "timestamp", "description": "首次产生流水的时间", "title": "首次流水时间"}, "final_score": {"bsonType": "number", "description": "用户在该房间的最终分数", "default": 0, "title": "最终分数"}, "is_settled": {"bsonType": "bool", "description": "是否已结算胜负统计", "default": false, "title": "结算状态"}, "game_result": {"bsonType": "string", "description": "游戏结果：win胜利，lose失败，draw平局", "enum": ["win", "lose", "draw", "unfinished"], "default": "unfinished", "title": "游戏结果"}, "create_time": {"bsonType": "timestamp", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}, "title": "创建时间"}, "update_time": {"bsonType": "timestamp", "description": "记录更新时间", "title": "更新时间"}}, "permission": {"read": "auth.uid && doc.user_id == auth.uid", "create": false, "update": false, "delete": false, "count": false}, "index": [{"IndexName": "user_room_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}, {"Name": "room_id", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "user_id_settled", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}, {"Name": "is_settled", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "room_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "room_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "create_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "create_time", "Direction": "-1"}], "MgoIsUnique": false}}]}