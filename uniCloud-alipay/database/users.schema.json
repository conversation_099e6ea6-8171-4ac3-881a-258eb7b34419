{"bsonType": "object", "description": "随心记分用户表", "required": ["wx_openid", "nickname"], "properties": {"_id": {"description": "用户ID，系统自动生成"}, "wx_openid": {"bsonType": "string", "description": "微信小程序openid，用户唯一标识", "pattern": "^[a-zA-Z0-9_-]{28}$", "title": "微信OpenID"}, "wx_unionid": {"bsonType": "string", "description": "微信unionid，跨应用用户识别", "title": "微信UnionID"}, "nickname": {"bsonType": "string", "description": "用户昵称，可修改", "minLength": 1, "maxLength": 20, "title": "用户昵称"}, "avatar_fileId": {"bsonType": "string", "description": "用户头像云存储文件ID", "title": "头像文件ID"}, "signature": {"bsonType": "string", "description": "个性签名", "maxLength": 100, "title": "个性签名"}, "gender": {"bsonType": "int", "description": "性别：0未知，1男，2女", "minimum": 0, "maximum": 2, "default": 0, "title": "性别"}, "total_games": {"bsonType": "int", "description": "总游戏局数", "minimum": 0, "default": 0, "title": "总游戏局数"}, "win_games": {"bsonType": "int", "description": "胜局数", "minimum": 0, "default": 0, "title": "胜局数"}, "lose_games": {"bsonType": "int", "description": "败局数", "minimum": 0, "default": 0, "title": "败局数"}, "created_rooms": {"bsonType": "int", "description": "创建的房间总数", "minimum": 0, "default": 0, "title": "创建房间数"}, "participated_rooms": {"bsonType": "int", "description": "参与的房间总数（有流水记录的房间）", "minimum": 0, "default": 0, "title": "参与房间数"}, "user_level": {"bsonType": "int", "description": "用户等级", "minimum": 1, "default": 1, "title": "用户等级"}, "total_score": {"bsonType": "int", "description": "总积分", "minimum": 0, "default": 0, "title": "总积分"}, "status": {"bsonType": "int", "description": "账户状态：0正常，1禁用", "enum": [0, 1], "default": 0, "title": "账户状态"}, "register_time": {"bsonType": "timestamp", "description": "注册时间", "forceDefaultValue": {"$env": "now"}, "title": "注册时间"}, "last_login_time": {"bsonType": "timestamp", "description": "最后登录时间", "title": "最后登录时间"}, "last_login_ip": {"bsonType": "string", "description": "最后登录IP地址", "title": "最后登录IP"}, "login_count": {"bsonType": "int", "description": "登录次数", "minimum": 0, "default": 0, "title": "登录次数"}, "active_room_id": {"bsonType": "string", "description": "用户当前活跃房间的文档ID，null表示无活跃房间", "title": "活跃房间ID"}}, "permission": {"read": "doc._id == auth.uid || 'CLIENTDB_MULTI_READ'", "create": false, "update": "(doc._id == auth.uid) && Object.keys(data).every(key => ['nickname', 'avatar_fileId', 'signature', 'gender'].includes(key))", "delete": false, "count": false}, "index": [{"IndexName": "wx_openid_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "wx_openid", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "wx_unionid", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "wx_unionid", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "register_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "register_time", "Direction": "-1"}], "MgoIsUnique": false}}, {"IndexName": "active_room_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "active_room_id", "Direction": "1"}], "MgoIsUnique": false}}]}