{"bsonType": "object", "description": "随心记分房间表", "required": ["room_id", "room_name", "creator_id", "game_type"], "properties": {"_id": {"description": "房间文档ID，系统自动生成"}, "room_id": {"bsonType": "string", "description": "房间唯一标识，格式：SX+8位数字", "pattern": "^SX[0-9]{8}$", "title": "房间ID"}, "room_name": {"bsonType": "string", "description": "房间名称，格式：四字吉庆词+数字", "minLength": 1, "maxLength": 20, "title": "房间名称"}, "creator_id": {"bsonType": "string", "description": "创建者用户ID，关联users表", "title": "创建者ID"}, "game_type": {"bsonType": "string", "description": "游戏类型：classic经典玩法，points给分玩法", "enum": ["classic", "points"], "title": "游戏类型"}, "room_status": {"bsonType": "string", "description": "房间状态：waiting等待中，playing进行中，finished已结束", "enum": ["waiting", "playing", "finished"], "default": "waiting", "title": "房间状态"}, "max_players": {"bsonType": "int", "description": "最大玩家数量，默认8", "minimum": 2, "maximum": 8, "default": 8, "title": "最大玩家数"}, "current_players": {"bsonType": "int", "description": "当前玩家数量", "minimum": 0, "default": 0, "title": "当前玩家数"}, "players": {"bsonType": "array", "description": "玩家列表数组", "items": {"bsonType": "object", "properties": {"user_id": {"bsonType": "string", "description": "玩家用户ID"}, "nickname": {"bsonType": "string", "description": "玩家昵称"}, "avatar_fileId": {"bsonType": "string", "description": "玩家头像云存储文件ID"}, "join_time": {"bsonType": "timestamp", "description": "加入时间"}, "is_creator": {"bsonType": "bool", "description": "是否为房主"}, "has_left": {"bsonType": "bool", "description": "是否已退出", "default": false}}}, "title": "玩家列表"}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}, "title": "创建时间"}, "start_time": {"bsonType": "timestamp", "description": "开始时间", "title": "开始时间"}, "end_time": {"bsonType": "timestamp", "description": "结束时间", "title": "结束时间"}, "game_rounds": {"bsonType": "int", "description": "游戏轮数统计", "minimum": 0, "default": 0, "title": "游戏轮数"}, "tea_water_limit_amount": {"bsonType": ["number", "null"], "description": "茶水金额上限，null表示不限制", "minimum": 0, "default": null, "title": "茶水金额上限"}, "tea_water_ratio": {"bsonType": "number", "description": "每笔抽取比例，范围0-10", "minimum": 0, "maximum": 10, "default": 0, "title": "每笔抽取比例"}, "tea_water_balance": {"bsonType": "number", "description": "当前茶水余额", "minimum": 0, "default": 0, "title": "当前茶水余额"}}, "permission": {"read": "auth.uid && (doc.creator_id == auth.uid || doc.players.some(player => player.user_id == auth.uid))", "create": false, "update": false, "delete": false, "count": false}, "index": [{"IndexName": "room_id_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "room_id", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "creator_status_composite", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "creator_id", "Direction": "1"}, {"Name": "room_status", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "creator_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "creator_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "room_status", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "room_status", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "create_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "create_time", "Direction": "-1"}], "MgoIsUnique": false}}]}