{"name": "room-websocket", "version": "2.0.0", "description": "WebSocket云函数用于房间实时消息同步 - 支持多玩家实时同步", "main": "index.js", "keywords": ["websocket", "unicloud", "room", "realtime", "multiplayer", "sync"], "author": "suixin-scorer", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {"jsonwebtoken": "^9.0.0", "number-precision": "^1.6.0"}, "cloudfunction-config": {"memorySize": 512, "timeout": 60}}