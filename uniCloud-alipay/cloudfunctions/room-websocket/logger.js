// ==================== 高级日志系统 ====================

/**
 * 日志级别定义
 */
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

/**
 * 当前日志级别 (可通过环境变量配置)
 * 0=DEBUG, 1=INFO, 2=WARN, 3=ERROR
 */
const CURRENT_LOG_LEVEL = process.env.LOG_LEVEL ? parseInt(process.env.LOG_LEVEL) : LOG_LEVELS.DEBUG;

/**
 * 请求跟踪存储
 */
const requestTracker = new Map();

/**
 * 性能统计数据
 */
const performanceStats = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  avgResponseTime: 0,
  operationStats: {},
  connectionStats: {
    totalConnections: 0,
    activeConnections: 0,
    failedConnections: 0
  }
};

/**
 * 日志工具类
 */
class Logger {
  /**
   * 格式化时间戳
   */
  static formatTimestamp() {
    return new Date().toISOString();
  }

  /**
   * 生成日志前缀
   */
  static getLogPrefix(level, module = 'WEBSOCKET', requestId = null) {
    const timestamp = this.formatTimestamp();
    const prefix = `[${timestamp}] [${level}] [${module}]`;
    return requestId ? `${prefix} [REQ:${requestId}]` : prefix;
  }

  /**
   * 检查是否应该输出指定级别的日志
   */
  static shouldLog(level) {
    return LOG_LEVELS[level] >= CURRENT_LOG_LEVEL;
  }

  /**
   * 格式化复杂对象
   */
  static formatObject(obj, maxDepth = 3) {
    if (maxDepth <= 0) return '[MAX_DEPTH_REACHED]';
    
    if (obj === null) return 'null';
    if (obj === undefined) return 'undefined';
    if (typeof obj === 'string') return obj.length > 200 ? obj.substring(0, 200) + '...' : obj;
    if (typeof obj === 'number' || typeof obj === 'boolean') return obj.toString();
    
    if (Array.isArray(obj)) {
      if (obj.length > 10) {
        return `Array(${obj.length})[${obj.slice(0, 3).map(item => this.formatObject(item, maxDepth - 1)).join(', ')}...]`;
      }
      return `[${obj.map(item => this.formatObject(item, maxDepth - 1)).join(', ')}]`;
    }
    
    if (typeof obj === 'object') {
      const keys = Object.keys(obj);
      if (keys.length > 10) {
        const preview = keys.slice(0, 3).map(key => `${key}: ${this.formatObject(obj[key], maxDepth - 1)}`).join(', ');
        return `{${preview}... +${keys.length - 3} more}`;
      }
      const content = keys.map(key => `${key}: ${this.formatObject(obj[key], maxDepth - 1)}`).join(', ');
      return `{${content}}`;
    }
    
    return obj.toString();
  }

  /**
   * DEBUG级别日志
   */
  static debug(message, data = null, module = 'WEBSOCKET', requestId = null) {
    if (!this.shouldLog('DEBUG')) return;
    const prefix = this.getLogPrefix('DEBUG', module, requestId);
    if (data) {
      console.log(`${prefix} ${message}`, this.formatObject(data));
    } else {
      console.log(`${prefix} ${message}`);
    }
  }

  /**
   * INFO级别日志
   */
  static info(message, data = null, module = 'WEBSOCKET', requestId = null) {
    if (!this.shouldLog('INFO')) return;
    const prefix = this.getLogPrefix('INFO', module, requestId);
    if (data) {
      console.log(`${prefix} ${message}`, this.formatObject(data));
    } else {
      console.log(`${prefix} ${message}`);
    }
  }

  /**
   * WARN级别日志
   */
  static warn(message, data = null, module = 'WEBSOCKET', requestId = null) {
    if (!this.shouldLog('WARN')) return;
    const prefix = this.getLogPrefix('WARN', module, requestId);
    if (data) {
      console.warn(`${prefix} ${message}`, this.formatObject(data));
    } else {
      console.warn(`${prefix} ${message}`);
    }
  }

  /**
   * ERROR级别日志
   */
  static error(message, error = null, module = 'WEBSOCKET', requestId = null) {
    if (!this.shouldLog('ERROR')) return;
    const prefix = this.getLogPrefix('ERROR', module, requestId);
    if (error) {
      const errorInfo = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      };
      console.error(`${prefix} ${message}`, this.formatObject(errorInfo));
    } else {
      console.error(`${prefix} ${message}`);
    }
  }

  /**
   * 性能日志
   */
  static performance(operation, duration, details = null, requestId = null) {
    const prefix = this.getLogPrefix('PERF', 'PERFORMANCE', requestId);
    const message = `${operation} 耗时: ${duration}ms`;
    
    if (details) {
      console.log(`${prefix} ${message}`, this.formatObject(details));
    } else {
      console.log(`${prefix} ${message}`);
    }

    // 更新性能统计
    this.updatePerformanceStats(operation, duration, true);
  }

  /**
   * 数据库操作日志
   */
  static database(operation, collection, condition = null, result = null, duration = null, requestId = null) {
    const prefix = this.getLogPrefix('DB', 'DATABASE', requestId);
    const message = `${operation} ${collection}${duration ? ` (${duration}ms)` : ''}`;
    
    const dbInfo = {
      operation,
      collection,
      condition: condition ? this.formatObject(condition, 2) : null,
      result: result ? {
        affected: result.updated || result.deleted || result.inserted || (result.data ? result.data.length : 0),
        total: result.total || null
      } : null
    };

    console.log(`${prefix} ${message}`, this.formatObject(dbInfo));
  }

  /**
   * 业务操作日志
   */
  static business(operation, userId, roomId, details = null, requestId = null) {
    const prefix = this.getLogPrefix('BIZ', 'BUSINESS', requestId);
    const message = `${operation} - 用户:${userId} 房间:${roomId}`;
    
    if (details) {
      console.log(`${prefix} ${message}`, this.formatObject(details));
    } else {
      console.log(`${prefix} ${message}`);
    }
  }

  /**
   * 连接事件日志
   */
  static connection(event, connectionId, userInfo = null, roomId = null, details = null) {
    const prefix = this.getLogPrefix('CONN', 'CONNECTION');
    const message = `${event} - 连接:${connectionId}${userInfo ? ` 用户:${userInfo.nickname}(${userInfo._id})` : ''}${roomId ? ` 房间:${roomId}` : ''}`;
    
    if (details) {
      console.log(`${prefix} ${message}`, this.formatObject(details));
    } else {
      console.log(`${prefix} ${message}`);
    }

    // 更新连接统计
    this.updateConnectionStats(event);
  }

  /**
   * 消息广播日志
   */
  static broadcast(roomId, messageType, targetCount, successCount, failedCount, details = null, requestId = null) {
    const prefix = this.getLogPrefix('BROADCAST', 'BROADCAST', requestId);
    const message = `房间:${roomId} 消息:${messageType} 目标:${targetCount} 成功:${successCount} 失败:${failedCount}`;
    
    if (details) {
      console.log(`${prefix} ${message}`, this.formatObject(details));
    } else {
      console.log(`${prefix} ${message}`);
    }
  }

  /**
   * 更新性能统计
   */
  static updatePerformanceStats(operation, duration, success) {
    performanceStats.totalRequests++;
    
    if (success) {
      performanceStats.successfulRequests++;
    } else {
      performanceStats.failedRequests++;
    }

    // 更新平均响应时间
    const totalTime = performanceStats.avgResponseTime * (performanceStats.totalRequests - 1) + duration;
    performanceStats.avgResponseTime = Math.round(totalTime / performanceStats.totalRequests);

    // 更新操作统计
    if (!performanceStats.operationStats[operation]) {
      performanceStats.operationStats[operation] = {
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity,
        successCount: 0,
        failCount: 0
      };
    }

    const opStats = performanceStats.operationStats[operation];
    opStats.count++;
    opStats.totalDuration += duration;
    opStats.avgDuration = Math.round(opStats.totalDuration / opStats.count);
    opStats.maxDuration = Math.max(opStats.maxDuration, duration);
    opStats.minDuration = Math.min(opStats.minDuration, duration);
    
    if (success) {
      opStats.successCount++;
    } else {
      opStats.failCount++;
    }
  }

  /**
   * 更新连接统计
   */
  static updateConnectionStats(event) {
    switch (event) {
      case 'CONNECT':
      case 'JOIN':
        performanceStats.connectionStats.totalConnections++;
        performanceStats.connectionStats.activeConnections++;
        break;
      case 'DISCONNECT':
      case 'LEAVE':
        performanceStats.connectionStats.activeConnections = Math.max(0, performanceStats.connectionStats.activeConnections - 1);
        break;
      case 'FAILED':
      case 'ERROR':
        performanceStats.connectionStats.failedConnections++;
        break;
    }
  }

  /**
   * 获取性能统计报告
   */
  static getPerformanceReport() {
    const report = {
      ...performanceStats,
      timestamp: this.formatTimestamp(),
      uptime: process.uptime ? `${Math.floor(process.uptime())}s` : 'N/A'
    };
    
    this.info('性能统计报告', report, 'PERFORMANCE');
    return report;
  }

  /**
   * 开始请求跟踪
   */
  static startRequest(requestId, operation, params = null) {
    const startTime = Date.now();
    requestTracker.set(requestId, {
      operation,
      startTime,
      params: params ? this.formatObject(params, 2) : null
    });
    
    this.debug(`请求开始: ${operation}`, { requestId, params }, 'REQUEST', requestId);
    return startTime;
  }

  /**
   * 结束请求跟踪
   */
  static endRequest(requestId, success = true, result = null, error = null) {
    const requestInfo = requestTracker.get(requestId);
    if (!requestInfo) {
      this.warn(`未找到请求跟踪信息: ${requestId}`);
      return;
    }

    const duration = Date.now() - requestInfo.startTime;
    const { operation, params } = requestInfo;

    if (success) {
      this.info(`请求成功: ${operation} (${duration}ms)`, { 
        requestId, 
        params, 
        result: result ? this.formatObject(result, 2) : null,
        duration 
      }, 'REQUEST', requestId);
    } else {
      this.error(`请求失败: ${operation} (${duration}ms)`, error, 'REQUEST', requestId);
    }

    // 记录性能数据
    this.updatePerformanceStats(operation, duration, success);
    
    // 清理跟踪信息
    requestTracker.delete(requestId);
  }

  /**
   * 安全数据格式化（移除敏感信息）
   */
  static sanitizeData(data) {
    if (!data || typeof data !== 'object') return data;
    
    const sensitiveKeys = ['token', 'password', 'secret', 'authorization', 'auth'];
    const sanitized = JSON.parse(JSON.stringify(data));
    
    const sanitizeRecursive = (obj) => {
      if (Array.isArray(obj)) {
        return obj.map(item => sanitizeRecursive(item));
      }
      
      if (obj && typeof obj === 'object') {
        const result = {};
        for (const [key, value] of Object.entries(obj)) {
          if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
            result[key] = '***REDACTED***';
          } else {
            result[key] = sanitizeRecursive(value);
          }
        }
        return result;
      }
      
      return obj;
    };

    return sanitizeRecursive(sanitized);
  }
}

/**
 * 定期打印性能报告（每10分钟）
 */
setInterval(() => {
  Logger.getPerformanceReport();
}, 10 * 60 * 1000);

module.exports = Logger; 