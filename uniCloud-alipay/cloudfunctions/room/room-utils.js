// 房间工具函数集合

// 吉庆四字词汇库
const auspiciousNames = [
  "红红火火", "恭喜发财", "金玉满堂", "花开富贵",
  "年年有余", "步步高升", "心想事成", "万事如意",
  "财源广进", "生意兴隆", "家和万事", "吉祥如意"
];

/**
 * 生成房间ID
 * 格式：SX + 8位数字
 * @returns {string} 房间ID
 */
function generateRoomId() {
  // 生成8位随机数字，确保唯一性
  const timestamp = Date.now().toString().slice(-6); // 取当前时间戳后6位
  const random = Math.floor(Math.random() * 100).toString().padStart(2, '0'); // 2位随机数
  return `SX${timestamp}${random}`;
}

/**
 * 生成房间名称
 * 格式：四字吉庆词 + 5位数字
 * @returns {string} 房间名称
 */
function generateRoomName() {
  // 随机选择一个吉庆词汇
  const randomIndex = Math.floor(Math.random() * auspiciousNames.length);
  const auspiciousWord = auspiciousNames[randomIndex];
  
  // 生成5位数字，起始值10000
  const randomNumber = Math.floor(Math.random() * 90000) + 10000;
  
  return `${auspiciousWord}${randomNumber}`;
}

/**
 * 验证用户是否有活跃房间
 * @param {string} userId 用户ID
 * @param {object} roomsCollection 房间集合对象
 * @param {object} db 数据库实例对象
 * @param {object} usersCollection 用户集合对象（新增参数）
 * @returns {object} 验证结果
 */
async function validateUserActiveRoom(userId, roomsCollection, db, usersCollection = null) {
  try {
    // 优先使用active_room_id字段查询（如果提供了usersCollection）
    if (usersCollection) {
      const userRes = await usersCollection.doc(userId).get();
      
      if (userRes.data.length === 0) {
        return {
          hasActiveRoom: false,
          activeRoom: null,
          error: '用户不存在'
        };
      }

      const user = userRes.data[0];
      
      if (!user.active_room_id) {
        return {
          hasActiveRoom: false,
          activeRoom: null
        };
      }

      // 通过active_room_id直接查询房间
      const roomRes = await roomsCollection.doc(user.active_room_id).get();
      
      if (roomRes.data.length === 0) {
        // 房间不存在，清理用户的active_room_id
        console.warn(`用户${userId}的活跃房间${user.active_room_id}不存在，准备清理`);
        await clearUserActiveRoom(userId, usersCollection);
        return {
          hasActiveRoom: false,
          activeRoom: null
        };
      }

      const room = roomRes.data[0];
      
      // 验证房间状态和用户是否在房间中
      if (room.room_status === 'finished') {
        await clearUserActiveRoom(userId, usersCollection);
        return {
          hasActiveRoom: false,
          activeRoom: null
        };
      }

      const userInRoom = room.players.some(player => player.user_id === userId && player.has_left !== true);
      if (!userInRoom) {
        console.warn(`用户${userId}不在其活跃房间${user.active_room_id}中，准备清理`);
        await clearUserActiveRoom(userId, usersCollection);
        return {
          hasActiveRoom: false,
          activeRoom: null
        };
      }

      return {
        hasActiveRoom: true,
        activeRoom: room
      };
    }

    // 兜底逻辑：扫描rooms表查找活跃房间（向后兼容）
    console.warn('未提供usersCollection，使用兜底查询逻辑');
    const activeRoomRes = await roomsCollection
      .where({
        creator_id: userId,
        room_status: db.command.in(['waiting', 'playing'])
      })
      .get();

    if (activeRoomRes.data.length > 0) {
      return {
        hasActiveRoom: true,
        activeRoom: activeRoomRes.data[0]
      };
    }

    // 查找用户作为玩家参与的活跃房间
    const participateRoomRes = await roomsCollection
      .where({
        room_status: db.command.in(['waiting', 'playing']),
        'players.user_id': userId
      })
      .get();

    if (participateRoomRes.data.length > 0) {
      return {
        hasActiveRoom: true,
        activeRoom: participateRoomRes.data[0]
      };
    }

    return {
      hasActiveRoom: false,
      activeRoom: null
    };
  } catch (error) {
    console.error('验证用户活跃房间失败:', error);
    return {
      hasActiveRoom: false,
      activeRoom: null,
      error: error.message
    };
  }
}

/**
 * 检查并自动结束房间
 * 当所有玩家都已退出时，自动将房间状态设置为finished
 * @param {string} roomId 房间ID
 * @param {object} roomsCollection 房间集合对象
 * @returns {object} 检查结果
 */
async function checkAndAutoFinishRoom(roomId, roomsCollection) {
  try {
    // 获取房间信息
    const roomRes = await roomsCollection
      .where({ room_id: roomId })
      .get();

    if (roomRes.data.length === 0) {
      return {
        success: false,
        message: '房间不存在'
      };
    }

    const room = roomRes.data[0];

    // 检查是否所有玩家都已退出
    // 改为基于has_left字段判断，而不是依赖current_players字段
    const activePlayers = room.players.filter(player => player.has_left !== true);
    if (activePlayers.length === 0) {
      // 所有玩家都已退出，自动结束房间
      await roomsCollection.doc(room._id).update({
        room_status: 'finished',
        end_time: new Date()
      });

      return {
        success: true,
        autoFinished: true,
        message: '房间已自动结束'
      };
    }

    return {
      success: true,
      autoFinished: false,
      message: `房间仍有${activePlayers.length}个活跃玩家，继续进行`
    };
  } catch (error) {
    console.error('检查自动结束房间失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

/**
 * 计算房间持续时间显示
 * @param {Date} createTime 创建时间
 * @returns {string} 时间显示文本
 */
function formatRoomDuration(createTime) {
  const now = new Date();
  const diff = now - new Date(createTime);
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}小时${minutes}分`;
  } else {
    return `${minutes}分钟`;
  }
}

/**
 * 清理用户活跃房间状态
 * @param {string} userId 用户ID
 * @param {object} usersCollection 用户集合对象
 * @returns {object} 清理结果
 */
async function clearUserActiveRoom(userId, usersCollection) {
  try {
    if (!userId) {
      return {
        success: false,
        message: '用户ID不能为空'
      };
    }

    await usersCollection.doc(userId).update({
      active_room_id: null
    });

    return {
      success: true,
      message: '用户活跃房间状态已清理'
    };
  } catch (error) {
    console.error('清理用户活跃房间状态失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

/**
 * 批量清理房间玩家的活跃状态
 * @param {Array} userIds 用户ID数组
 * @param {object} usersCollection 用户集合对象
 * @returns {object} 清理结果
 */
async function batchClearActiveRoom(userIds, usersCollection) {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return {
        success: false,
        message: '用户ID数组不能为空'
      };
    }

    // 批量更新用户的活跃房间状态
    const updatePromises = userIds.map(userId => 
      usersCollection.doc(userId).update({
        active_room_id: null
      }).catch(error => {
        console.error(`清理用户${userId}活跃房间状态失败:`, error);
        return { error: error.message, userId };
      })
    );

    const results = await Promise.all(updatePromises);
    const failed = results.filter(result => result && result.error);

    return {
      success: true,
      message: `批量清理完成，成功${userIds.length - failed.length}个，失败${failed.length}个`,
      failed: failed
    };
  } catch (error) {
    console.error('批量清理用户活跃房间状态失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

/**
 * 验证并修复数据一致性
 * @param {string} userId 用户ID
 * @param {object} usersCollection 用户集合对象
 * @param {object} roomsCollection 房间集合对象
 * @returns {object} 验证结果
 */
async function validateAndRepairActiveRoomConsistency(userId, usersCollection, roomsCollection) {
  try {
    // 获取用户信息
    const userRes = await usersCollection.doc(userId).get();
    if (userRes.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const user = userRes.data[0];
    const activeRoomId = user.active_room_id;

    if (!activeRoomId) {
      return {
        success: true,
        message: '用户无活跃房间，数据一致'
      };
    }

    // 验证房间是否存在且用户在其中
    const roomRes = await roomsCollection.doc(activeRoomId).get();
    if (roomRes.data.length === 0) {
      // 房间不存在，清理用户的活跃房间状态
      await clearUserActiveRoom(userId, usersCollection);
      return {
        success: true,
        message: '房间不存在，已清理用户活跃房间状态',
        repaired: true
      };
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(player => player.user_id === userId && player.has_left !== true);

    if (!userInRoom) {
      // 用户不在房间中，清理活跃房间状态
      await clearUserActiveRoom(userId, usersCollection);
      return {
        success: true,
        message: '用户不在房间中，已清理活跃房间状态',
        repaired: true
      };
    }

    // 检查房间状态
    if (room.room_status === 'finished') {
      // 房间已结束，清理活跃房间状态
      await clearUserActiveRoom(userId, usersCollection);
      return {
        success: true,
        message: '房间已结束，已清理活跃房间状态',
        repaired: true
      };
    }

    return {
      success: true,
      message: '数据一致性验证通过'
    };
  } catch (error) {
    console.error('验证数据一致性失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

module.exports = {
  generateRoomId,
  generateRoomName,
  validateUserActiveRoom,
  checkAndAutoFinishRoom,
  formatRoomDuration,
  clearUserActiveRoom,
  batchClearActiveRoom,
  validateAndRepairActiveRoomConsistency
};

// ==================== 测试函数（仅在开发环境使用） ====================





