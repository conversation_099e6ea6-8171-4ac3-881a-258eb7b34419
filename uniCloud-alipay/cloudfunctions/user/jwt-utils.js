const jwt = require("jsonwebtoken");

// JWT密钥，生产环境应从环境变量获取
const JWT_SECRET = "suixin-scorer-jwt-secret";
const TOKEN_EXPIRES_IN = "7d"; // 7天

/**
 * 生成JWT token
 * @param {string} openId 微信openId
 * @returns {string} JWT token
 */
function generateToken(openId) {
  const payload = {
    openId: openId,
  };

  const options = {
    expiresIn: TOKEN_EXPIRES_IN,
    issuer: "suixin-scorer",
    subject: openId,
  };

  return jwt.sign(payload, JWT_SECRET, options);
}

/**
 * 验证JWT token
 * @param {string} token JWT token
 * @returns {Object} 验证结果 {valid: boolean, payload: Object, error: string}
 */
function verifyToken(token) {
  try {
    if (!token) {
      return { valid: false, error: "Token为空" };
    }

    const options = {
      issuer: "suixin-scorer",
    };

    const payload = jwt.verify(token, JWT_SECRET, options);

    return { valid: true, payload };
  } catch (error) {
    let errorMessage = "Token验证失败";
    if (error.name === "TokenExpiredError") {
      errorMessage = "Token已过期";
    } else if (error.name === "JsonWebTokenError") {
      errorMessage = "Token格式错误";
    } else if (error.name === "NotBeforeError") {
      errorMessage = "Token未生效";
    }

    return { valid: false, error: errorMessage };
  }
}

module.exports = {
  generateToken,
  verifyToken,
};
