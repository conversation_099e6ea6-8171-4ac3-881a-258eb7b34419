const jwt = require("jsonwebtoken");
const JWT_SECRET = "suixin-scorer-jwt-secret";

/**
 * 验证JWT token
 * @param {string} token JWT token
 * @returns {Object} 验证结果 {valid: boolean, payload: Object, error: string}
 */
function verifyToken(token) {
  try {
    if (!token) {
      return { valid: false, error: "Token为空" };
    }

    const options = {
      issuer: "suixin-scorer",
    };

    const payload = jwt.verify(token, JWT_SECRET, options);

    return { valid: true, payload };
  } catch (error) {
    let errorMessage = "Token验证失败";
    if (error.name === "TokenExpiredError") {
      errorMessage = "Token已过期";
    } else if (error.name === "JsonWebTokenError") {
      errorMessage = "Token格式错误";
    } else if (error.name === "NotBeforeError") {
      errorMessage = "Token未生效";
    }

    return { valid: false, error: errorMessage };
  }
}

// 私有方法：验证token并获取用户信息
async function _verifyTokenAndGetUser(token, usersCollection) {
  const verifyResult = verifyToken(token);
  if (!verifyResult.valid) {
    return {
      success: false,
      code: 401,
      message: "Token无效，请重新登录",
    };
  }

  const userRes = await usersCollection
    .where({ wx_openid: verifyResult.payload.openId })
    .get();

  if (userRes.data.length === 0) {
    return {
      success: false,
      code: 404,
      message: "用户不存在",
    };
  }

  return {
    success: true,
    data: {
      openId: verifyResult.payload.openId,
      userInfo: userRes.data[0],
    },
  };
}
/**
 * 获取微信access_token（自动检查有效性并刷新）
 * @returns {Object} access_token获取结果
 */
async function getAccessToken() {
  try {
    // 1. 先从数据库获取当前的access_token
    const configRes = await this.systemConfigCollection
      .where({ config_key: "wx_access_token" })
      .get();

    let needRefresh = true;
    let currentToken = null;

    if (configRes.data.length > 0) {
      const tokenConfig = configRes.data[0];
      const expiresAt = new Date(tokenConfig.expires_at);
      const now = new Date();

      // 提前5分钟刷新token
      const refreshThreshold = new Date(now.getTime() + 5 * 60 * 1000);

      if (expiresAt > refreshThreshold) {
        // token还有效，直接返回
        needRefresh = false;
        currentToken = tokenConfig.config_value;
      }
    }

    if (!needRefresh && currentToken) {
      return {
        code: 200,
        message: "获取access_token成功",
        data: {
          access_token: currentToken,
        },
      };
    }

    // 2. 需要刷新token，调用微信API
    const wxApiUrl = "https://api.weixin.qq.com/cgi-bin/token";
    const url = `${wxApiUrl}?grant_type=client_credential&appid=${this.appId}&secret=${this.appSecret}`;

    const result = await uniCloud.httpclient.request(url, {
      method: "GET",
      dataType: "json",
    });

    if (result.data.errcode) {
      return {
        code: 400,
        message: `获取access_token失败: ${result.data.errmsg}`,
      };
    }

    const newAccessToken = result.data.access_token;
    const expiresIn = result.data.expires_in || 7200; // 默认2小时
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    // 3. 更新或插入数据库
    if (configRes.data.length > 0) {
      // 更新现有记录
      await this.systemConfigCollection.doc(configRes.data[0]._id).update({
        config_value: newAccessToken,
        expires_at: expiresAt,
        update_time: new Date(),
      });
    } else {
      // 创建新记录
      await this.systemConfigCollection.add({
        config_key: "wx_access_token",
        config_value: newAccessToken,
        expires_at: expiresAt,
        description: "微信小程序access_token",
      });
    }

    return {
      code: 200,
      message: "获取access_token成功",
      data: {
        access_token: newAccessToken,
      },
    };
  } catch (error) {
    console.error("获取access_token失败:", error);
    return {
      code: 500,
      message: "获取access_token服务异常",
    };
  }
}
module.exports = {
  // 云对象初始化
  _before: function () {
    this.db = uniCloud.database();
    this.systemConfigCollection = this.db.collection("system_config");
    this.usersCollection = this.db.collection("users");
    this.appId = "wx02b0f59c159c575e";
    this.appSecret = "95490954cef249c2062603a76b06e83c";
  },

  /**
   * 生成房间小程序码
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @returns {Object} 小程序码生成结果
   */
  async generateQRCode(token, roomId) {
    try {
      if (!token || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      // 获取access_token
      const accessTokenResult = await getAccessToken.call(this);
      console.log("accessTokenResult", accessTokenResult);
      if (accessTokenResult.code !== 200) {
        return {
          code: accessTokenResult.code,
          message: accessTokenResult.message,
        };
      }

      const accessToken = accessTokenResult.data.access_token;

      // 调用微信API生成小程序码
      const wxApiUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit";
      const url = `${wxApiUrl}?access_token=${accessToken}`;

      const requestData = {
        scene: `roomId=${roomId}`, // 房间ID作为scene参数
        page: "pages/join/index", // 小程序页面路径
        check_path: false,
        // env_version: "release", // 正式版
        // env_version: "develop", // 开发版
        env_version: "trial", // 体验版
        width: 430,
        auto_color: false,
        line_color: { r: 0, g: 0, b: 0 },
        is_hyaline: false,
      };

      const result = await uniCloud.httpclient.request(url, {
        method: "POST",
        data: JSON.stringify(requestData),
        responseType: "buffer",
      });
      // 检查是否返回错误
      if (result.data.errcode) {
        return {
          code: 400,
          message: `生成小程序码失败: ${result.data.errmsg}`,
        };
      }
      // 将buffer转换为base64供前端使用
      const base64Image = result.data.toString("base64");

      return {
        code: 200,
        message: "生成小程序码成功",
        data: {
          qrcode: `data:image/png;base64,${base64Image}`,
          roomId: roomId,
        },
      };
    } catch (error) {
      console.error("生成小程序码失败:", error);
      return {
        code: 500,
        message: "生成小程序码服务异常",
      };
    }
  },
};
