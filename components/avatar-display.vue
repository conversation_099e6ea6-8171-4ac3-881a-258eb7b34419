<template>
  <div class="avatar-display" :style="{ width: size, height: size }">
    <image
      v-if="displayAvatar"
      :src="displayAvatar"
      :style="{ width: size, height: size }"
      mode="aspectFill"
      class="avatar-image"
      @error="handleImageError"
    />
    <div
      v-else-if="isLoading"
      class="avatar-loading"
      :style="{ width: size, height: size }"
    >
      <div class="loading-spinner"></div>
    </div>
    <image
      v-else
      :src="defaultAvatar"
      :style="{ width: size, height: size }"
      mode="aspectFill"
      class="avatar-image"
    />
  </div>
</template>

<script>
export default {
  name: "AvatarDisplay",
  props: {
    avatarFileId: {
      type: String,
      default: null,
    },
    size: {
      type: String,
      default: "50px",
    },
    defaultAvatar: {
      type: String,
      default:
        "https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/default-avatar.jpg",
    },
  },
  data() {
    return {
      displayAvatar: null,
      isLoading: false,
      hasError: false,
    };
  },
  watch: {
    avatarFileId: {
      handler(newFileId) {
        this.loadAvatar(newFileId);
      },
      immediate: true,
    },
  },
  methods: {
    async loadAvatar(fileId) {
      // 如果没有fileId，直接使用默认头像
      if (!fileId) {
        this.displayAvatar = null;
        this.isLoading = false;
        this.hasError = false;
        return;
      }

      try {
        this.isLoading = true;
        this.hasError = false;

        // 调用云对象获取base64数据
        uniCloud.getTempFileURL({
          fileList: [fileId],
        }).then(res => {
          this.displayAvatar = res.fileList[0].tempFileURL;
        }).catch(err => {
          this.displayAvatar = null;
          throw new Error(err || "获取头像失败");
        });
      } catch (error) {
        console.error("加载头像失败:", error);
        this.hasError = true;
        this.displayAvatar = null;
      } finally {
        this.isLoading = false;
      }
    },

    handleImageError() {
      console.warn("头像图片加载失败，使用默认头像");
      this.displayAvatar = null;
      this.hasError = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.avatar-display {
  position: relative;
  overflow: hidden;
  display: inline-block;

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 50%;

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e0e0e0;
      border-top: 2px solid #07c160;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
