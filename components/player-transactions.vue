<template>
  <div class="player-transactions-modal" v-if="visible">
    <div class="player-transactions-content">
      <div class="modal-header">
        <div class="player-header">
          <div class="player-avatar">
            <avatar-display
                class="avatar-image"
                :avatarFileId="selectedPlayer.avatarFileId"
                size="36px"
                style="width: 36px; height: 36px"
              />
          </div>
          <div class="player-details">
            <div class="player-name">{{ selectedPlayer.name }}</div>
            <div class="transactions-count">流水记录：{{ displayTransactions.length }}</div>
          </div>
        </div>
        <div class="modal-close" @click="closeModal">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-label">总收入</div>
          <div class="stat-value positive">+{{ totalIncome.toFixed(2) }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">总支出</div>
          <div class="stat-value negative">{{ totalExpense.toFixed(2) }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">净收益</div>
          <div
            class="stat-value"
            :class="{ positive: netProfit >= 0, negative: netProfit < 0 }"
          >
            {{ netProfit >= 0 ? "+" : "" }}{{ netProfit.toFixed(2) }}
          </div>
        </div>
      </div>

      <!-- 流水列表 -->
      <div
        class="transactions-list-container"
        v-if="displayTransactions.length > 0"
      >
        <scroll-view
          class="transactions-scroll-view"
          scroll-y="true"
          :show-scrollbar="false"
        >
          <div class="transactions-list">
            <div
              v-for="(transaction, index) in displayTransactions"
              :key="transaction.id || index"
              class="transaction-item"
            >
              <div class="transaction-left">
                <div class="transaction-main">
                  <div class="transaction-type-badge">
                    {{ transaction.type }}
                  </div>
                  <div class="transaction-description">
                    {{ transaction.description }}
                  </div>
                </div>
                <div class="transaction-timestamp">
                  {{ transaction.timestamp }}
                </div>
              </div>
              <div class="transaction-right">
                <div
                  class="transaction-amount"
                  :class="{
                    positive: transaction.amount > 0,
                    negative: transaction.amount < 0,
                  }"
                >
                  {{
                    transaction.amount > 0
                      ? "+" + transaction.amount.toFixed(2)
                      : transaction.amount.toFixed(2)
                  }}
                </div>
              </div>
            </div>
          </div>
        </scroll-view>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <span class="iconfont empty-wallet">&#xe879;</span>
        </div>
        <div class="empty-text">{{ selectedPlayer.name }}还没有任何流水</div>
      </div>
    </div>
  </div>
</template>

<script>
import { compareTimestamps } from "@/utils/dateUtils.js";
import AvatarDisplay from "./avatar-display.vue";

export default {
  name: "PlayerTransactions",
  components: {
    AvatarDisplay,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectedPlayer: {
      type: Object,
      default: () => ({}),
    },
    playerTransactions: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    displayTransactions() {
      // 根据选中的玩家返回对应的流水记录
      if (!this.selectedPlayer || !this.selectedPlayer.id) {
        return [];
      }

      const playerId = this.selectedPlayer.id;
      const transactions = this.playerTransactions[playerId] || [];

      // 按时间倒序排列，最新的在前面
      return transactions.sort((a, b) => {
        return compareTimestamps(a.timestamp, b.timestamp);
      });
    },

    totalIncome() {
      return this.displayTransactions
        .filter((t) => t.amount > 0)
        .reduce((sum, t) => sum + t.amount, 0);
    },

    totalExpense() {
      return this.displayTransactions
        .filter((t) => t.amount < 0)
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);
    },

    netProfit() {
      return this.totalIncome - this.totalExpense;
    },
  },
  methods: {
    closeModal() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 个人流水弹窗样式 */
.player-transactions-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: flex-end;
}

.player-transactions-content {
  @include modal-content;
  width: 100%;
  background-color: $card-background;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  max-height: 75vh;
  max-width: none;
  position: relative;
  border: 1px solid $border-light;
  border-bottom: none;
  box-shadow: $box-shadow-lg;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: $spacing-md $spacing-lg;

  .player-header {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .player-avatar {
      @include avatar(36px);
    }

    .player-details {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;

      .player-name {
        font-size: $font-size-lg;
        color: $text-primary;
        font-weight: $font-weight-semibold;
      }

      .transactions-count {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

/* 统计信息样式 */
.stats-section {
  display: flex;
  justify-content: space-between;
  gap: $spacing-md;
  padding: $spacing-lg;
  background-color: $card-background;
  border-bottom: 1px solid $border-light;
  margin: 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-md $spacing-sm;
  background-color: $background-color;
  border-radius: $border-radius-xs;
  border: 1px solid $border-light;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-secondary;
  font-weight: $font-weight-medium;
  text-align: center;
  white-space: nowrap;
}

.stat-value {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  text-align: center;
  
  &.positive {
    color: $positive-amount-color;
  }
  
  &.negative {
    color: $negative-amount-color;
  }
  
  &.zero {
    color: $text-muted;
  }
}

.transactions-list-container {
  flex: 1;
  overflow: hidden;
  padding: 0 $spacing-lg;
}

.transactions-scroll-view {
  width: 100%;
  height: 100%;
  min-height: 250px;
  max-height: calc(75vh - 230px);
}

.transactions-list {
  width: 100%;
  padding: $spacing-sm 0;
}

.transaction-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md 0;
  border-bottom: 1px solid $border-light;

  &:last-child {
    border-bottom: none;
  }
}

.transaction-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.transaction-main {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.transaction-type-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 20px;
  padding: 0 $spacing-xs;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  
  &.win {
    background-color: rgba(250, 81, 81, 0.1);
    color: $positive-amount-color;
  }
  
  &.lose {
    background-color: rgba(7, 193, 96, 0.1);
    color: $negative-amount-color;
  }
  
  &.distribute {
    background-color: rgba(245, 158, 11, 0.1);
    color: $warning-color;
  }
  
  &.collect {
    background-color: rgba(59, 130, 246, 0.1);
    color: $info-color;
  }
  
  &.setting {
    background-color: rgba(107, 114, 128, 0.1);
    color: $text-muted;
  }
}

.transaction-description {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.transaction-timestamp {
  font-size: $font-size-xs;
  color: $text-muted;
}

.transaction-right {
  display: flex;
  align-items: center;
}

.transaction-amount {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;

  &.positive {
    color: $positive-amount-color;
  }

  &.negative {
    color: $negative-amount-color;
  }

  &.zero {
    color: $text-muted;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  min-height: 250px;
}

.empty-icon {
  font-size: 48px;
  color: $text-muted;
  margin-bottom: $spacing-md;

  .empty-wallet {
    font-size: 48px;
    opacity: 0.6;
  }
}

.empty-text {
  font-size: $font-size-base;
  color: $text-muted;
  text-align: center;
}
</style>
