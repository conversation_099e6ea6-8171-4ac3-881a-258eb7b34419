<template>
  <div class="invite-modal" v-if="visible">
    <div class="invite-modal-content">
      <div class="modal-header">
        <div class="modal-title">邀请好友</div>
        <div class="modal-close" @click="closeModal">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <div class="modal-body">
        <div class="invite-subtitle">房间还没有人，邀请好友扫码加入房间</div>

        <div class="qrcode-container">
          <image
            v-if="qrcodeImage"
            :src="qrcodeImage"
            class="qrcode-image"
            mode="aspectFit"
          />
          <div v-else-if="loadingQR" class="qrcode-loading">
            <span class="loading-text">生成中...</span>
          </div>
          <div v-else class="qrcode-placeholder">
            <span class="placeholder-text">二维码生成失败</span>
          </div>
        </div>

        <button open-type="share" class="invite-button">
          邀请朋友加入房间
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import loginFun from "@/utils/login.js";

export default {
  name: "InviteFriends",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    roomId: {
      type: String,
      required: true,
    },
    roomName: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      qrcodeImage: null,
      loadingQR: false,
    };
  },
  watch: {
    visible(newVal) {
      if (newVal && this.roomId) {
        this.generateQRCode();
      }
    },
  },
  
  methods: {
    closeModal() {
      this.qrcodeImage = null;
      this.loadingQR = false;
      this.$emit("close");
    },

    /**
     * 生成房间二维码
     */
    async generateQRCode() {
      if (!this.roomId) {
        uni.showToast({
          title: "房间ID不存在",
          icon: "none",
        });
        return;
      }

      this.loadingQR = true;
      this.qrcodeImage = null;

      try {
        const token = loginFun.storage.getToken();
        if (!token) {
          throw new Error("用户未登录");
        }

        // 调用wxapi云对象生成二维码，传递房间ID和房间名
        const wxapiObj = uniCloud.importObject("wxapi");
        const result = await wxapiObj.generateQRCode(token, this.roomId);

        if (result.code === 200) {
          this.qrcodeImage = result.data.qrcode;
          console.log("二维码生成成功，包含房间信息:", {
            roomId: result.data.roomId,
          });
        } else {
          throw new Error(result.message || "生成二维码失败");
        }
      } catch (error) {
        console.error("生成二维码失败:", error);
        uni.showToast({
          title: error.message || "生成二维码失败",
          icon: "none",
        });
      } finally {
        this.loadingQR = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 邀请好友弹窗样式 */
.invite-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: center;
}

.invite-modal-content {
  @include modal-content;
  width: 80%;
  max-width: 360px;
  background-color: $card-background;
  border-radius: $border-radius-lg;
  max-height: none;
  border: 1px solid $border-light;
  box-shadow: $box-shadow-lg;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;

  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.modal-body {
  padding: $spacing-xl;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 300px;
  max-height: 400px;
}

.invite-subtitle {
  font-size: $font-size-sm;
  color: $text-secondary;
  margin-bottom: $spacing-xl;
  text-align: center;
  line-height: 1.5;
}

.qrcode-container {
  width: 180px;
  height: 180px;
  background-color: $text-white;
  border: 1px solid $border-light;
  border-radius: $border-radius-base;
  margin-bottom: $spacing-xl;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  box-shadow: $box-shadow-sm;

  .qrcode-image {
    width: 100%;
    height: 100%;
    border-radius: $border-radius-base;
  }

  .qrcode-loading,
  .qrcode-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-text,
    .placeholder-text {
      color: $text-muted;
      font-size: $font-size-sm;
    }
  }
}

.invite-button {
  @include button-primary;
  width: 100%;
  height: 44px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-xl;
  margin-top: auto;

  &:active {
    background-color: $primary-dark;
  }
}
</style>
