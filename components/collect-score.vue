<template>
  <div class="collect-modal" v-if="visible">
    <div class="collect-modal-content">
      <div class="modal-header">
        <div class="modal-title">收分</div>
        <div class="modal-close" @click="closeModal">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <div class="collect-form">
        <div class="form-item">
          <input
            class="collect-input"
            type="number"
            v-model="collectAmount"
            placeholder="请输入收分数额"
            min="1"
            step="1"
            pattern="[0-9]*"
            @input="handleCollectInput"
          />
        </div>
        
        <button class="confirm-collect-button" @click="confirmCollect">
          确定收分
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CollectScore",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tableScore: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      collectAmount: null,
    };
  },
  methods: {
    closeModal() {
      this.collectAmount = null;
      this.$emit("close");
    },
    confirmCollect() {
      // 处理收分的逻辑
      if (!this.collectAmount) {
        uni.showToast({
          title: "请输入收分数额",
          icon: "none",
        });
        return;
      }

      const amount = Number(this.collectAmount);

      // 验证输入是否为正整数
      if (isNaN(amount) || amount <= 0 || !Number.isInteger(amount)) {
        uni.showToast({
          title: "请输入有效的正整数",
          icon: "none",
        });
        return;
      }

      // 验证分数不能超过牌桌现有分数
      if (amount > this.tableScore) {
        uni.showToast({
          title: `收分不能超过桌面现有分数${this.tableScore}`,
          icon: "none",
        });
        return;
      }

      // 验证分数不能超过99999
      if (amount > 99999) {
        uni.showToast({
          title: "分数不能超过99999",
          icon: "none",
        });
        return;
      }

      // 发送确认事件给父组件
      this.$emit("confirm", amount);

      // 清空输入并关闭弹窗
      this.collectAmount = null;
    },
    handleCollectInput(event) {
      // 处理输入框的输入事件，确保只输入数字字符
      const inputValue = event.target.value;

      if (inputValue) {
        // 移除非数字字符
        const cleanValue = inputValue.replace(/[^0-9]/g, "");
        this.collectAmount = cleanValue;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 收分弹窗样式 */
.collect-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
}

.collect-modal-content {
  @include modal-content;
  background-color: $card-background;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.collect-form {
  width: 100%;
  padding: $spacing-xl;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.form-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.collect-input {
  @include input-base;
  width: 100%;
  max-width: 300px;
  height: 50px;
  font-size: $font-size-lg;
  text-align: center;
  font-weight: $font-weight-medium;
}

.confirm-collect-button {
  @include button-primary;
  width: 100%;
  height: 44px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-base;

  &:active {
    background-color: $primary-dark;
  }
}
</style>
