<template>
  <div class="profile-editor-modal" @click.self="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <div class="modal-title">设置头像和昵称</div>
        <div class="close-button" @click="closeModal">
          <span class="iconfont close-icon">✕</span>
        </div>
      </div>

      <div class="modal-body">
        <div class="avatar-area">
          <button
            class="avatar-wrapper"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar"
          >
            <!-- 使用AvatarDisplay组件显示头像 -->
            <avatar-display
              :avatarFileId="tempUserData.avatar_fileId"
              :defaultAvatar="tempAvatarUrl || defaultAvatar"
              size="100px"
              style="width: 100px; height: 100px"
            />
            <div class="avatar-overlay">
              <span class="iconfont camera-icon">&#xe885;</span>
              <span class="change-text">更换头像</span>
            </div>
          </button>
        </div>

        <div class="nickname-area">
          <div class="nickname-input-wrapper">
            <input
              type="nickname"
              v-model="tempUserData.username"
              class="nickname-input"
              placeholder="在这里填写6字以内昵称"
              maxlength="6"
              @click.stop
            />
            <div class="dice-button" @click.stop="generateRandomNickname">
              <span class="iconfont random-icon">&#xe880;</span>
            </div>
          </div>
          <div class="input-hint">点击右侧骰子随机生成昵称</div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="save-button" @click.stop="saveUserProfile">
          确认保存
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  name: "ProfileEditor",
  components: {
    AvatarDisplay,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    userData: {
      type: Object,
      default: () => ({
        username: "",
        avatar_fileId: "",
        signature: "",
        gender: 0,
      }),
    },
  },
  data() {
    return {
      tempUserData: {
        username: "",
        avatar_fileId: "",
        signature: "",
        gender: 0,
      },
      tempAvatarUrl: "", // 临时头像地址，用于上传
      defaultAvatar:
        "https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/default-avatar.jpg",
      randomNicknames: [
        "小宝",
        "赢家",
        "高手",
        "大佬",
        "麻神",
        "财神",
        "小妹",
        "牌王",
      ],
    };
  },
  watch: {
    userData: {
      handler(newValue) {
        // 复制用户数据到临时变量，包含所有字段
        this.tempUserData = {
          username: newValue.username || "",
          avatar_fileId: newValue.avatar_fileId || "",
          signature: newValue.signature || "",
          gender: newValue.gender || 0,
        };
      },
      immediate: true,
    },
  },
  methods: {
    closeModal() {
      this.$emit("close");
    },
    generateRandomNickname() {
      // 随机生成昵称
      const randomIndex = Math.floor(
        Math.random() * this.randomNicknames.length
      );
      this.tempUserData.username = this.randomNicknames[randomIndex];
    },
    validateUserData() {
      // 验证昵称和头像
      if (
        !this.tempUserData.username ||
        this.tempUserData.username.trim() === ""
      ) {
        // 使用uni.showToast替代alert
        uni.showToast({
          title: "请设置昵称",
          icon: "none",
        });
        return false;
      }

      if (this.tempUserData.username.length > 6) {
        uni.showToast({
          title: "昵称不能超过6个字符",
          icon: "none",
        });
        return false;
      }

      // 移除头像必填验证，允许不设置头像（使用默认头像）

      return true;
    },
    async saveUserProfile() {
      // 验证表单
      if (!this.validateUserData()) {
        return;
      }

      try {
        uni.showLoading({
          title: "保存中...",
          mask: true,
        });

        let finalAvatarFileId = this.tempUserData.avatar_fileId;

        // 检查头像是否为临时地址，如果是则先上传到云存储
        if (this.tempAvatarUrl && this.isTemporaryAvatar(this.tempAvatarUrl)) {
          console.log("检测到临时头像地址，开始上传到云存储...");
          finalAvatarFileId = await this.uploadAvatarToCloud(
            this.tempAvatarUrl
          );
        }

        // 将更新后的用户数据通过事件发送给父组件，包含所有字段
        this.$emit("save", {
          username: this.tempUserData.username,
          avatar_fileId: finalAvatarFileId,
          signature: this.tempUserData.signature,
          gender: this.tempUserData.gender,
        });

        // 关闭弹窗
        this.closeModal();
      } catch (error) {
        console.error("保存用户资料失败:", error);
        uni.showToast({
          title: "保存失败，请重试",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },

    /**
     * 判断是否为临时头像地址
     */
    isTemporaryAvatar(avatarUrl) {
      if (!avatarUrl) return false;

      // 微信小程序临时文件路径特征
      // 临时文件通常包含 tmp、wxfile、usr 等关键词
      const tempPatterns = [
        /^wxfile:\/\//, // 微信文件协议
        /\/tmp\//, // 临时目录
        /\/usr\//, // 用户临时目录
        /tempFilePath/, // 临时文件路径
        /\.tmp$/, // .tmp 结尾
      ];

      return tempPatterns.some((pattern) => pattern.test(avatarUrl));
    },
    onChooseAvatar(e) {
      // 处理微信头像选择回调
      const { avatarUrl } = e.detail;
      console.log(e, "chooseAvatar");

      if (avatarUrl) {
        // 保存临时头像地址，等待用户保存时再上传
        this.tempAvatarUrl = avatarUrl;
      } else {
        uni.showToast({
          title: "获取头像失败，请重试",
          icon: "none",
        });
      }
    },

    /**
     * 上传头像到uniCloud云存储
     */
    async uploadAvatarToCloud(avatarUrl) {
      try {
        // 生成唯一的文件名
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substring(2, 8);
        const fileName = `avatar_${timestamp}_${randomStr}.jpg`;

        // 设置云存储路径 - 根据支付宝云规范设置路径
        const cloudPath = `avatars/${fileName}`;

        // 上传文件到uniCloud云存储
        const uploadResult = await uniCloud.uploadFile({
          filePath: avatarUrl, // 临时文件路径
          cloudPath: cloudPath, // 云存储中的文件路径
        });

        console.log("头像上传成功:", uploadResult);

        if (uploadResult.fileID) {
          // 上传成功，返回fileID而不是base64
          return uploadResult.fileID;
        } else {
          throw new Error("上传失败：未获取到文件ID");
        }
      } catch (error) {
        console.error("头像上传失败:", error);

        // 上传失败时，返回空，使用默认头像
        console.warn("头像上传失败，将使用默认头像");
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.profile-editor-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: center;

  .modal-content {
    @include modal-content;
    width: 85%;
    max-width: 380px;
    background-color: $card-background;
    border: 1px solid $border-light;
    box-shadow: $box-shadow-lg;

    .modal-header {
      @include modal-header;
      background-color: $card-background;
      border-bottom: 1px solid $border-color;
      border-radius: $border-radius-lg $border-radius-lg 0 0;
      padding: $spacing-md $spacing-lg;

      .modal-title {
        font-size: $font-size-lg;
        color: $text-primary;
        font-weight: $font-weight-semibold;
      }

      .close-button {
        @include button-text;
        width: 32px;
        height: 32px;
        padding: 0;
        color: $text-muted;
        border-radius: $border-radius-round;
        font-size: $font-size-lg;

        &:hover {
          background-color: $background-color;
          color: $text-secondary;
        }

        &:active {
          background-color: $border-light;
        }

        .close-icon {
          font-size: 18px;
        }
      }
    }

    .modal-body {
      padding: $spacing-xl $spacing-lg;

      .avatar-area {
        display: flex;
        justify-content: center;
        margin-bottom: $spacing-xl;

        .avatar-wrapper {
          @include avatar(100px);
          position: relative;
          border: 3px solid $border-light;
          padding: 0;
          outline: none;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: $primary-color;
            transform: scale(1.02);
          }

          &:active {
            border-color: $primary-dark;
            transform: scale(0.98);
          }

          // image {
          //   width: 100%;
          //   height: 100%;
          //   object-fit: cover;
          // }

          .avatar-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: $text-white;
            opacity: 0;
            transition: opacity 0.2s ease;
            border-radius: inherit;

            .camera-icon {
              font-size: 24px;
              margin-bottom: $spacing-xs;
            }

            .change-text {
              font-size: $font-size-xs;
            }
          }

          &:hover .avatar-overlay {
            opacity: 1;
          }
        }
      }

      .nickname-area {
        .nickname-input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          margin-bottom: $spacing-sm;

          .nickname-input {
            @include input-base;
            padding-left: $spacing-base;
            padding-right: 56px;
            height: 50px;
            font-size: $font-size-base;
            width: 100%;
            transition: all 0.2s ease;

            &:focus {
              border-color: $primary-color;
              box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
            }
          }

          .dice-button {
            position: absolute;
            right: $spacing-md;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: $border-radius-base;
            cursor: pointer;
            font-size: 16px;
            color: $text-secondary;
            background-color: $background-color;
            transition: all 0.2s ease;

            &:hover {
              background-color: $border-light;
              color: $text-primary;
            }

            &:active {
              background-color: $primary-color;
              color: $text-white;
              transform: scale(0.95);
            }

            .random-icon {
              font-size: 16px;
            }
          }
        }

        .input-hint {
          font-size: $font-size-sm;
          color: $text-muted;
          text-align: center;
          line-height: 1.4;
        }
      }
    }

    .modal-footer {
      padding: 0 $spacing-lg $spacing-xl;

      .save-button {
        @include button-primary;
        width: 100%;
        height: 44px;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        border-radius: $border-radius-xl;

        &:active {
          background-color: $primary-dark;
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
