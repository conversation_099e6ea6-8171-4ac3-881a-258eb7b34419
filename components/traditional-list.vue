<template>
  <div class="traditional-list">
    <div
      v-for="(item, index) in processedList"
      :key="index"
      class="record-item"
      :class="item.type"
    >
      <!-- 记分项 -->
      <template v-if="item.type === 'score'">
        <!-- 当前用户的记分 -->
        <div v-if="item.isSelf" class="score-right my-message">
          <div class="timestamp my-timestamp">
            {{ item.timestamp }} {{ item.sender.name }}
            <span class="me-badge">我</span>
          </div>
          <div class="score-content">
            <div class="score-avatar my-avatar">
              <avatar-display
                class="avatar-image"
                :avatarFileId="item.sender.avatar_fileId"
                size="36px"
              />
            </div>
            <div class="score-message my-score-message">
              <div class="score-text">
                计分给
                <text class="target-name">{{ item.target.name }}</text>
              </div>
              <div
                class="score-amount my-score-amount"
                :class="{
                  positive: item._isPositive,
                  negative: !item._isPositive,
                }"
              >
                {{ item._formattedAmount }}
              </div>
            </div>
          </div>
        </div>

        <!-- 其他用户的记分 -->
        <div v-else class="score-left">
          <div class="timestamp">
            {{ item.timestamp }} {{ item.sender.name }}
          </div>
          <div class="score-content">
            <div class="score-avatar">
              <avatar-display
                class="avatar-image"
                :avatarFileId="item.sender.avatar_fileId"
                size="36px"
              />
            </div>
            <div class="score-message">
              <div class="score-text">
                计分给
                <text class="target-name">{{ item.target.name }}</text>
              </div>
              <div
                class="score-amount"
                :class="{
                  positive: item._isPositive,
                  negative: !item._isPositive,
                }"
              >
                {{ item._formattedAmount }}
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 系统提示 -->
      <template v-else-if="item.type === 'system'">
        <div class="system-message">
          <text class="user-name" v-if="item.actionType === 'tea_settings'">{{
            item?.sender?.name
          }}</text>
          {{ item?.message }}
        </div>
      </template>

      <!-- 结算单 -->
      <template v-else-if="item.type === 'settlement'">
        <div
          class="settlement-record"
          :class="{ 'my-settlement': item.creator.isSelf }"
        >
          <div class="settlement-header">
            <div class="settlement-icon"></div>
            <div class="settlement-title">
              <span class="creator-name">{{ item.creator.name }}</span>
              生成了结算单
              <span v-if="item.creator.isSelf" class="me-badge">我</span>
            </div>
          </div>
          <div class="settlement-details">
            <div
              class="settlement-item"
              v-for="(detail, dIndex) in item.details"
              :key="dIndex"
            >
              <div class="settlement-from">
                <span
                  :class="{ 'creator-name': detail.from === item.creator.name }"
                  >{{ detail.from }}</span
                >
                向
                <span
                  :class="{ 'creator-name': detail.to === item.creator.name }"
                  >{{ detail.to }}</span
                >
                付
              </div>
              <div class="settlement-amount">
                {{ detail.amount }}
              </div>
            </div>
            <div class="settlement-total">
              <div class="settlement-label">小计 {{ item.totalLabel }}</div>
              <div
                class="settlement-amount"
                :class="{ negative: item.totalAmount < 0 }"
              >
                {{ formatAmount(item.totalAmount) }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  name: "TraditionalList",
  components: {
    AvatarDisplay,
  },
  props: {
    list: {
      type: Array,
      required: true,
    },
    currentUserId: {
      type: String,
      required: true,
    },
  },
  computed: {
    /**
     * 预处理消息列表，计算每条消息的显示信息
     */
    processedList() {
      return this.list.map((item) => {
        console.log(item, "item");
        if (item.type === "score") {
          const scoreInfo = this.getScoreDisplayInfo(item);
          return {
            ...item,
            _displayAmount: scoreInfo.displayAmount,
            _isPositive: scoreInfo.isPositive,
            _formattedAmount: this.formatAmount(scoreInfo.displayAmount),
          };
        }
        return item;
      });
    },
  },
  methods: {
    formatAmount(amount) {
      const prefix = amount > 0 ? "+" : "";
      return `${prefix}${amount.toFixed(2)}`;
    },

    /**
     * 根据分数流向计算实际显示的金额和样式
     * @param {Object} item 消息项
     * @returns {Object} { displayAmount: number, isPositive: boolean }
     */
    getScoreDisplayInfo(item) {
      if (item.type !== "score" || !item.sender || !item.target) {
        return { displayAmount: 0, isPositive: false };
      }

      const amount = item.amount || 0;
      const senderId = item.sender.id;
      const targetId = item.target.id;

      // 判断当前用户是发送者还是接收者
      if (senderId === this.currentUserId) {
        // 当前用户是发送者（付款方），显示为负数
        return { displayAmount: -amount, isPositive: false };
      } else if (targetId === this.currentUserId) {
        // 当前用户是接收者（收款方），显示为正数
        return { displayAmount: amount, isPositive: true };
      } else {
        // 当前用户既不是发送者也不是接收者，这种情况不应该出现在用户的消息列表中
        // 但为了容错，按原始金额显示
        return { displayAmount: amount, isPositive: amount > 0 };
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";
.traditional-list {
  width: 100%;
  padding: $spacing-sm;
  box-sizing: border-box;
}

.record-item {
  margin-bottom: $spacing-sm;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

/* 系统消息样式 */
.system-message {
  @include card;
  background-color: rgba(87, 107, 149, 0.1);
  border: 1px solid rgba(87, 107, 149, 0.2);
  border-radius: $border-radius-base;
  padding: $spacing-base $spacing-md;
  font-size: $font-size-sm;
  color: $text-secondary;
  text-align: center;
  margin: $spacing-md auto;
  align-self: center;
  max-width: 85%;
  box-shadow: $box-shadow-sm;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(87, 107, 149, 0.15);
    transform: translateY(-1px);
    box-shadow: $box-shadow-base;
  }
}

.user-name {
  color: $secondary-color;
  font-weight: $font-weight-semibold;
  margin-right: $spacing-xs;
}

/* "我"的标识徽章 */
.me-badge {
  display: inline-block;
  background: linear-gradient(135deg, $primary-color, $primary-dark);
  color: $text-white;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  padding: 2rpx 6rpx;
  border-radius: $border-radius-base;
  margin-left: $spacing-xs;
  box-shadow: $box-shadow-sm;
}

/* 记分消息样式 */
.score-left,
.score-right {
  max-width: 85%;
  box-sizing: border-box;
  margin-bottom: $spacing-sm;
}

.score-left {
  align-self: flex-start;
}

.score-right {
  align-self: flex-end;
}

/* "我"的消息特殊样式 */
.my-message {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -$spacing-xs;
    right: -$spacing-xs;
    bottom: -$spacing-xs;
    left: -$spacing-xs;
    background: linear-gradient(
      135deg,
      rgba(7, 193, 96, 0.05),
      rgba(6, 173, 86, 0.05)
    );
    border-radius: $border-radius-lg;
    z-index: -1;
    opacity: 0.6;
  }
}

.timestamp {
  font-size: $font-size-xs;
  color: $text-muted;
  margin-bottom: $spacing-xs;
  line-height: 1.2;
}

.score-right .timestamp {
  text-align: right;
}

/* "我"的时间戳特殊样式 */
.my-timestamp {
  color: $secondary-color;
  font-weight: $font-weight-medium;
}

.score-content {
  display: flex;
  align-items: flex-start;
  margin-top: $spacing-xs;
}

.score-right .score-content {
  flex-direction: row-reverse;
}

/* "我"的头像特殊样式 */
.my-avatar {
  box-shadow: $box-shadow-base;
  border: 2px solid $primary-color;
}

// .score-avatar image {
//   width: 100%;
//   height: 100%;
//   object-fit: cover;
// }

.avatar-image{
  width: 36px;
  height: 36px;
}
.score-message {
  @include card;
  background-color: $card-background;
  border: 1px solid $border-light;
  border-radius: $border-radius-lg;
  padding: $spacing-base $spacing-md;
  margin: 0 $spacing-sm;
  box-sizing: border-box;
  max-width: calc(100% - 62px);
  word-break: break-word;
  box-shadow: $box-shadow-sm;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;

  .score-left & {
    border-top-left-radius: 4px;
  }

  .score-right & {
    border-top-right-radius: 4px;
  }
}

/* "我"的消息气泡特殊样式 */
.my-score-message {
  background: linear-gradient(135deg, $primary-light, rgba(7, 193, 96, 0.08));
  border: 1px solid rgba(7, 193, 96, 0.3);
  box-shadow: $box-shadow-base;

  &:hover {
    transform: translateY(-1px);
    box-shadow: $box-shadow-lg;
    border-color: rgba(7, 193, 96, 0.5);
  }
}

.score-text {
  font-size: $font-size-base;
  color: $text-primary;
  line-height: 1.4;
  flex: 1;
  margin-right: $spacing-md;
}

.target-name {
  color: $secondary-color;
  font-weight: $font-weight-medium;
}

.score-amount {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  transition: transform 0.2s ease;
  flex-shrink: 0;

  &:hover {
    transform: scale(1.05);
  }
}

.score-amount.positive {
  color: $positive-amount-color;
}

.score-amount.negative {
  color: $negative-amount-color;
}

/* "我"的分数特殊样式 */
.my-score-amount {
  &.positive {
    color: $positive-amount-color;
  }

  &.negative {
    color: $negative-amount-color;
  }
}

/* 结算单样式 */
.settlement-record {
  @include card;
  background: linear-gradient(135deg, $warning-color, #e6855f);
  border-radius: $border-radius-lg;
  overflow: hidden;
  margin: $spacing-md auto;
  align-self: center;
  width: 90%;
  max-width: 420px;
  box-sizing: border-box;
  box-shadow: $box-shadow-base;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-lg;
  }
}

/* "我"创建的结算单特殊样式 */
.my-settlement {
  background: linear-gradient(135deg, $primary-color, $primary-dark);
  border: 2px solid rgba(7, 193, 96, 0.3);
  box-shadow: $box-shadow-lg;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(7, 193, 96, 0.3);
    border-color: rgba(7, 193, 96, 0.5);
  }
}

.settlement-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
  background-color: rgba(0, 0, 0, 0.1);
}

.settlement-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: $text-white;
  border-radius: $border-radius-round;
  margin-right: $spacing-sm;
  position: relative;

  &::after {
    content: "￥";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: $warning-color;
    font-weight: $font-weight-bold;
    font-size: $font-size-base;
  }
}

.settlement-title {
  color: $text-white;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
}

/* 结算单生成者名字特殊样式 */
.creator-name {
  color: $text-white;
  font-weight: $font-weight-semibold;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
}

.settlement-details {
  padding: $spacing-md;
}

.settlement-item {
  display: flex;
  justify-content: space-between;
  padding: $spacing-xs 0;
  align-items: center;
}

.settlement-from {
  color: rgba(255, 255, 255, 0.9);
  font-size: $font-size-base;
  flex: 1;
  padding-right: $spacing-sm;
}

.settlement-amount {
  color: $text-white;
  font-size: $font-size-md;
  font-weight: $font-weight-bold;
  flex-shrink: 0;
}

.settlement-total {
  display: flex;
  justify-content: space-between;
  border-top: 1px dashed rgba(255, 255, 255, 0.25);
  margin-top: $spacing-base;
  padding-top: $spacing-base;
  align-items: center;
}

.settlement-label {
  color: $text-white;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
}

.settlement-amount.negative {
  color: $text-white;
}

@media (max-width: 480px) {
  .score-left,
  .score-right {
    max-width: 90%;
  }

  .settlement-record {
    width: 95%;
  }

  .score-avatar {
    @include avatar(36px);
  }

  .score-message {
    max-width: calc(100% - 56px);
    padding: $spacing-sm $spacing-base;
  }

  .me-badge {
    font-size: 9px;
    padding: 1px 4px;
  }
}
</style>
