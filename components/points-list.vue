<template>
  <div class="points-list">
    <div
      v-for="(item, index) in processedList"
      :key="index"
      class="record-item"
      :class="item.type"
    >
      <!-- 出分项 -->
      <template v-if="item.type === 'distribute'">
        <!-- 当前用户的出分 -->
        <div v-if="item.isSelf" class="score-right my-message">
          <div class="timestamp my-timestamp">
            {{ item.timestamp }} {{ item.sender.name }}
            <span class="me-badge">我</span>
          </div>
          <div class="score-content">
            <div class="score-avatar my-avatar">
              <avatar-display
                class="avatar-image"
                :avatarFileId="item.sender.avatar_fileId"
                size="50px"
              />
            </div>
            <div class="score-message my-score-message">
              <div class="score-text">
                给分到桌面
              </div>
              <div
                class="score-amount my-score-amount negative"
              >
                {{ item._formattedAmount }}
              </div>
            </div>
          </div>
        </div>

        <!-- 其他用户的出分 -->
        <div v-else class="score-left">
          <div class="timestamp">
            {{ item.timestamp }} {{ item.sender.name }}
          </div>
          <div class="score-content">
            <div class="score-avatar">
              <avatar-display
                class="avatar-image"
                :avatarFileId="item.sender.avatar_fileId"
                size="50px"
              />
            </div>
            <div class="score-message">
              <div class="score-text">
                给分到桌面
              </div>
              <div
                class="score-amount negative"
              >
                {{ item._formattedAmount }}
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 收分项 -->
      <template v-else-if="item.type === 'collect'">
        <!-- 当前用户的收分 -->
        <div v-if="item.isSelf" class="score-right my-message">
          <div class="timestamp my-timestamp">
            {{ item.timestamp }} {{ item.sender.name }}
            <span class="me-badge">我</span>
          </div>
          <div class="score-content">
            <div class="score-avatar my-avatar">
              <avatar-display
                class="avatar-image"
                :avatarFileId="item.sender.avatar_fileId"
                size="50px"
              />
            </div>
            <div class="score-message my-score-message">
              <div class="score-text">
                从桌面收分
              </div>
              <div
                class="score-amount my-score-amount positive"
              >
                {{ item._formattedAmount }}
              </div>
            </div>
          </div>
        </div>

        <!-- 其他用户的收分 -->
        <div v-else class="score-left">
          <div class="timestamp">
            {{ item.timestamp }} {{ item.sender.name }}
          </div>
          <div class="score-content">
            <div class="score-avatar">
              <avatar-display
                class="avatar-image"
                :avatarFileId="item.sender.avatar_fileId"
                size="50px"
              />
            </div>
            <div class="score-message">
              <div class="score-text">
                从桌面收分
              </div>
              <div
                class="score-amount positive"
              >
                {{ item._formattedAmount }}
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 系统提示 -->
      <template v-else-if="item.type === 'system'">
        <div class="system-message">
          <text class="user-name" v-if="item.actionType === 'tea_settings'">{{
            item?.sender?.name
          }}</text>
          {{ item?.message }}
        </div>
      </template>

      <!-- 开局/结局消息 -->
      <template v-else-if="item.type === 'round_start' || item.type === 'round_end'">
        <div class="system-message">
          {{ item.type === 'round_start' ? `第${item.round_number}局开始` : `第${item.round_number}局结束` }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  name: "PointsList",
  components: {
    AvatarDisplay,
  },
  props: {
    list: {
      type: Array,
      required: true,
    },
    currentUserId: {
      type: String,
      required: true,
    },
  },
  computed: {
    /**
     * 预处理消息列表，计算每条消息的显示信息
     */
    processedList() {
      return this.list.map((item) => {
        console.log(item, "item");
        if (item.type === "distribute" || item.type === "collect") {
          const scoreInfo = this.getPointsDisplayInfo(item);
          return {
            ...item,
            _displayAmount: scoreInfo.displayAmount,
            _isPositive: scoreInfo.isPositive,
            _formattedAmount: this.formatAmount(scoreInfo.displayAmount),
            isSelf: item.sender && item.sender.id === this.currentUserId,
          };
        }
        return {
          ...item,
          isSelf: item.sender && item.sender.id === this.currentUserId,
        };
      });
    },
  },
  methods: {
    formatAmount(amount) {
      const prefix = amount > 0 ? "+" : "";
      return `${prefix}${amount.toFixed(2)}`;
    },

    /**
     * 根据给分/收分操作计算实际显示的金额和样式
     * @param {Object} item 消息项
     * @returns {Object} { displayAmount: number, isPositive: boolean }
     */
    getPointsDisplayInfo(item) {
      if (item.type === "distribute") {
        // 出分显示为负数
        const amount = item.amount || 0;
        return { displayAmount: -amount, isPositive: false };
      } else if (item.type === "collect") {
        // 收分显示为正数（使用实际到账金额）
        const amount = item.actual_amount || item.amount || 0;
        return { displayAmount: amount, isPositive: true };
      }
      
      return { displayAmount: 0, isPositive: false };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";
.points-list {
  width: 100%;
  padding: $spacing-sm;
  box-sizing: border-box;
}

.record-item {
  margin-bottom: $spacing-sm;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

/* 系统消息样式 */
.system-message {
  @include card;
  background-color: rgba(87, 107, 149, 0.1);
  border: 1px solid rgba(87, 107, 149, 0.2);
  border-radius: $border-radius-base;
  padding: $spacing-base $spacing-md;
  font-size: $font-size-sm;
  color: $text-secondary;
  text-align: center;
  margin: $spacing-md auto;
  align-self: center;
  max-width: 85%;
  box-shadow: $box-shadow-sm;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(87, 107, 149, 0.15);
    transform: translateY(-1px);
    box-shadow: $box-shadow-base;
  }
}

.user-name {
  color: $secondary-color;
  font-weight: $font-weight-semibold;
  margin-right: $spacing-xs;
}

/* "我"的标识徽章 */
.me-badge {
  display: inline-block;
  background: linear-gradient(135deg, $primary-color, $primary-dark);
  color: $text-white;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  padding: 2rpx 6rpx;
  border-radius: $border-radius-base;
  margin-left: $spacing-xs;
  box-shadow: $box-shadow-sm;
}

/* 记分消息样式 */
.score-left,
.score-right {
  max-width: 85%;
  box-sizing: border-box;
  margin-bottom: $spacing-sm;
}

.score-left {
  align-self: flex-start;
}

.score-right {
  align-self: flex-end;
}

/* "我"的消息特殊样式 */
.my-message {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -$spacing-xs;
    right: -$spacing-xs;
    bottom: -$spacing-xs;
    left: -$spacing-xs;
    background: linear-gradient(
      135deg,
      rgba(7, 193, 96, 0.05),
      rgba(6, 173, 86, 0.05)
    );
    border-radius: $border-radius-lg;
    z-index: -1;
    opacity: 0.6;
  }
}

.timestamp {
  font-size: $font-size-xs;
  color: $text-muted;
  margin-bottom: $spacing-xs;
  line-height: 1.2;
}

.score-right .timestamp {
  text-align: right;
}

/* "我"的时间戳特殊样式 */
.my-timestamp {
  color: $secondary-color;
  font-weight: $font-weight-medium;
}

.score-content {
  display: flex;
  align-items: flex-start;
  margin-top: $spacing-xs;
}

.score-right .score-content {
  flex-direction: row-reverse;
}

.score-avatar {
  @include avatar(42px);
  flex-shrink: 0;
  box-shadow: $box-shadow-sm;
  border: 2px solid $border-light;
}

/* "我"的头像特殊样式 */
.my-avatar {
  box-shadow: $box-shadow-base;
  border: 2px solid $primary-color;
}

// .score-avatar image {
//   width: 100%;
//   height: 100%;
//   object-fit: cover;
// }

.score-message {
  @include card;
  background-color: $card-background;
  border: 1px solid $border-light;
  border-radius: $border-radius-lg;
  padding: $spacing-base $spacing-md;
  margin: 0 $spacing-sm;
  box-sizing: border-box;
  max-width: calc(100% - 62px);
  word-break: break-word;
  box-shadow: $box-shadow-sm;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;

  .score-left & {
    border-top-left-radius: 4px;
  }

  .score-right & {
    border-top-right-radius: 4px;
  }
}

/* "我"的消息气泡特殊样式 */
.my-score-message {
  background: linear-gradient(135deg, $primary-light, rgba(7, 193, 96, 0.08));
  border: 1px solid rgba(7, 193, 96, 0.3);
  box-shadow: $box-shadow-base;

  &:hover {
    transform: translateY(-1px);
    box-shadow: $box-shadow-lg;
    border-color: rgba(7, 193, 96, 0.5);
  }
}

.score-text {
  font-size: $font-size-base;
  color: $text-primary;
  line-height: 1.4;
  flex: 1;
  margin-right: $spacing-md;
}

.score-amount {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  transition: transform 0.2s ease;
  flex-shrink: 0;

  &:hover {
    transform: scale(1.05);
  }
}

.score-amount.positive {
  color: $positive-amount-color;
}

.score-amount.negative {
  color: $negative-amount-color;
}

/* "我"的分数特殊样式 */
.my-score-amount {
  &.positive {
    color: $positive-amount-color;
  }

  &.negative {
    color: $negative-amount-color;
  }
}

@media (max-width: 480px) {
  .score-left,
  .score-right {
    max-width: 90%;
  }

  .score-avatar {
    @include avatar(36px);
  }

  .score-message {
    max-width: calc(100% - 56px);
    padding: $spacing-sm $spacing-base;
  }

  .me-badge {
    font-size: 9px;
    padding: 1px 4px;
  }
}
</style> 