<template>
  <div class="table-records-modal" v-if="visible">
    <div class="table-records-content">
      <div class="modal-header">
        <picker
          @change="onRoundChange"
          :value="selectedRoundIndex"
          :range="roundPickerOptions"
          mode="selector"
        >
          <div class="round-selector">
            牌桌第{{ selectedRound }}局记录
            <text class="dropdown-icon">▼</text>
          </div>
        </picker>
        <div class="modal-close" @click="handleClose">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <!-- 记录列表 -->
      <div class="records-list-container" v-if="displayRecords.length > 0">
        <scroll-view
          class="records-scroll-view"
          scroll-y="true"
          :show-scrollbar="false"
        >
          <div class="records-list">
            <div
              v-for="(record, index) in displayRecords"
              :key="record.id"
              class="record-item"
            >
              <div class="record-left">
                <div class="record-timestamp">{{ record.timestamp }}</div>
                <div class="record-details">
                  <span class="record-player">{{ record.playerName }}</span>
                  <span class="record-action">{{ record.action }}</span>
                </div>
              </div>
              <div class="record-right">
                <div
                  class="record-amount"
                  :class="{
                    positive: record.amount > 0,
                    negative: record.amount < 0,
                  }"
                >
                  {{
                    formatAmountDisplay(record.amount)
                  }}
                </div>
              </div>
            </div>
          </div>
        </scroll-view>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <span class="iconfont empty-folder">&#xe879;</span>
        </div>
        <div class="empty-text">桌面还没有任何记录呢</div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatAmountDisplay } from "@/utils/mathUtils.js";

export default {
  name: "TableRecords",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tableRecords: {
      type: Object,
      default: () => ({}),
    },
    currentRound: {
      type: Number,
      default: 1,
    },
    availableRounds: {
      type: Array,
      default: () => [1, 2, 3],
    },
  },
  data() {
    return {
      selectedRound: this.currentRound,
    };
  },
  computed: {
    displayRecords() {
      // 只显示选择局的记录
      return this.tableRecords[this.selectedRound] || [];
    },
    selectedRoundIndex() {
      // 返回当前选择局数在数组中的索引
      return this.availableRounds.indexOf(this.selectedRound);
    },
    roundPickerOptions() {
      // 返回选择器选项数组
      return this.availableRounds.map((round) => `第${round}局`);
    },
  },
  watch: {
    currentRound: {
      handler(newVal) {
        this.selectedRound = newVal;
      },
      immediate: true,
    },
  },
  methods: {
    formatAmountDisplay,
    handleClose() {
      this.$emit("close");
    },
    onRoundChange(e) {
      // 处理局数选择变化
      const selectedIndex = e.detail.value;
      this.selectedRound = this.availableRounds[selectedIndex];

      // 显示提示
      uni.showToast({
        title: `切换到第${this.selectedRound}局记录`,
        icon: "none",
        duration: 1500,
      });

      // 触发事件，通知父组件局数变化
      this.$emit("round-change", this.selectedRound);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 桌面记录弹窗样式 */
.table-records-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: flex-end;
}

.table-records-content {
  @include modal-content;
  width: 100%;
  background-color: $card-background;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  max-height: 70vh;
  max-width: none;
  position: relative;
  border: 1px solid $border-light;
  border-bottom: none;
  box-shadow: $box-shadow-lg;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: $spacing-md $spacing-lg;
}

.round-selector {
  display: flex;
  align-items: center;
  font-size: $font-size-lg;
  color: $text-primary;
  font-weight: $font-weight-semibold;
  cursor: pointer;
  padding: $spacing-xs 0;
  transition: color 0.2s ease;

  &:hover {
    color: $primary-color;
  }
}

.dropdown-icon {
  margin-left: $spacing-sm;
  font-size: $font-size-sm;
  color: $text-muted;
  transition: transform 0.3s ease;
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.records-list-container {
  flex: 1;
  overflow: hidden;
  padding: 0 $spacing-lg;
}

.records-scroll-view {
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: calc(70vh - 80px);
}

.records-list {
  width: 100%;
  padding: $spacing-sm 0;
}

.record-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md 0;
  border-bottom: 1px solid $border-light;

  &:last-child {
    border-bottom: none;
  }
}

.record-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.record-timestamp {
  font-size: $font-size-xs;
  color: $text-muted;
}

.record-details {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.record-player {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.record-action {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.record-right {
  display: flex;
  align-items: center;
}

.record-amount {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;

  &.positive {
    color: $positive-amount-color;
  }

  &.negative {
    color: $negative-amount-color;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  min-height: 200px;
}

.empty-icon {
  font-size: 48px;
  color: $text-muted;
  margin-bottom: $spacing-md;

  .empty-folder {
    font-size: 48px;
    opacity: 0.6;
  }
}

.empty-text {
  font-size: $font-size-base;
  color: $text-muted;
  text-align: center;
}
</style>
