<template>
  <div class="base-modal" v-if="visible" @click="handleMaskClick">
    <div class="base-modal-content" @click.stop>
      <!-- 头部插槽 -->
      <div class="modal-header" v-if="$slots.header || title">
        <slot name="header">
          <div class="modal-title">{{ title }}</div>
          <div class="modal-close" @click="handleClose" v-if="showClose">
            <span class="iconfont close-icon">&#xe874;</span>
          </div>
        </slot>
      </div>

      <!-- 主体内容插槽 -->
      <div class="modal-body">
        <slot></slot>
      </div>

      <!-- 底部插槽 -->
      <div class="modal-footer" v-if="$slots.footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "BaseModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    showClose: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleMaskClick() {
      if (this.maskClosable) {
        this.handleClose();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.base-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
}

.base-modal-content {
  @include modal-content;
  background-color: $card-background;
  border: 1px solid $border-light;
  box-shadow: $box-shadow-lg;
  max-height: 80vh;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
    transform: scale(0.95);
  }

  .close-icon {
    font-size: 18px;
  }
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: $background-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $border-color;
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: $text-muted;
  }
}

.modal-footer {
  padding: $spacing-md $spacing-lg $spacing-lg;
  background-color: $card-background;
  border-top: 1px solid $border-light;
}
</style>
