<template>
  <div class="player-records-modal" v-if="visible">
    <div class="player-records-content">
      <div class="modal-header">
        <div class="player-header">
          <div class="player-avatar">
            <avatar-display
              style="width: 36px; height: 36px"
              class="avatar-image"
              :avatarFileId="selectedPlayer.avatarFileId"
              size="36px"
            />
          </div>
          <div class="player-details">
            <div class="player-name">{{ selectedPlayer.name }}</div>
            <div class="player-round">第{{ currentRound }}局</div>
          </div>
        </div>
        <div class="modal-close" @click="closeModal">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <!-- 记录列表 -->
      <div class="records-list-container" v-if="displayRecords.length > 0">
        <scroll-view
          class="records-scroll-view"
          scroll-y="true"
          :show-scrollbar="false"
        >
          <div class="records-list">
            <div
              v-for="(record, index) in displayRecords"
              :key="record.id"
              class="record-item"
            >
              <div class="record-left">
                <div class="record-timestamp">{{ record.timestamp }}</div>
                <div class="record-details">
                  <span class="record-player">{{ record.playerName }}</span>
                  <span class="record-action">{{ record.action }}</span>
                </div>
              </div>
              <div class="record-right">
                <div
                  class="record-amount"
                  :class="{
                    positive: record.amount > 0,
                    negative: record.amount < 0,
                  }"
                >
                  {{
                    record.amount > 0
                      ? "+" + record.amount.toFixed(2)
                      : record.amount.toFixed(2)
                  }}
                </div>
              </div>
            </div>
          </div>
        </scroll-view>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <span class="iconfont empty-user">&#xe879;</span>
        </div>
        <div class="empty-text">{{ selectedPlayer.name }}还没有任何记录呢</div>
      </div>
    </div>
  </div>
</template>

<script>
import AvatarDisplay from "./avatar-display.vue";
export default {
  name: "PlayerRecords",
  components: {
    AvatarDisplay,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectedPlayer: {
      type: Object,
      default: () => ({}),
    },
    currentRound: {
      type: Number,
      default: 1,
    },
    playerRecords: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    displayRecords() {
      // 根据选中的玩家和当前局数返回对应的记录
      if (!this.selectedPlayer || !this.selectedPlayer.id) {
        return [];
      }

      const playerId = this.selectedPlayer.id;
      const playerRecords = this.playerRecords[playerId];

      if (!playerRecords) {
        return [];
      }

      return playerRecords[this.currentRound] || [];
    },
  },
  methods: {
    closeModal() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 个人记录弹窗样式 */
.player-records-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: flex-end;
}

.player-records-content {
  @include modal-content;
  width: 100%;
  background-color: $card-background;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  max-height: 70vh;
  max-width: none;
  position: relative;
  border: 1px solid $border-light;
  border-bottom: none;
  box-shadow: $box-shadow-lg;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: $spacing-md $spacing-lg;

  .player-header {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .player-avatar {
      @include avatar(36px);
    }

    .player-details {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;

      .player-name {
        font-size: $font-size-lg;
        color: $text-primary;
        font-weight: $font-weight-semibold;
      }

      .player-round {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;

  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.records-list-container {
  flex: 1;
  overflow: hidden;
  padding: 0 $spacing-lg;
}

.records-scroll-view {
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: calc(70vh - 80px);
}

.records-list {
  width: 100%;
  padding: $spacing-sm 0;
}

.record-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md 0;
  border-bottom: 1px solid $border-light;

  &:last-child {
    border-bottom: none;
  }
}

.record-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.record-timestamp {
  font-size: $font-size-xs;
  color: $text-muted;
}

.record-details {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.record-action {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.record-target {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.record-right {
  display: flex;
  align-items: center;
}

.record-amount {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;

  &.positive {
    color: $positive-amount-color;
  }

  &.negative {
    color: $negative-amount-color;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  min-height: 200px;
}

.empty-icon {
  font-size: 48px;
  color: $text-muted;
  margin-bottom: $spacing-md;

  .empty-user {
    font-size: 48px;
    opacity: 0.6;
  }
}

.empty-text {
  font-size: $font-size-base;
  color: $text-muted;
  text-align: center;
}
</style>
