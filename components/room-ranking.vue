<template>
  <div class="ranking-modal" v-if="visible">
    <div class="ranking-modal-content">
      <div class="modal-header">
        <div class="modal-title">房间排行榜单</div>
        <div class="modal-close" @click="handleClose">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>
      <div class="ranking-list-container">
        <div class="ranking-list">
          <div
            v-for="(player, index) in sortedRankingList"
            :key="player.id || 'tea'"
            class="ranking-item"
          >
            <div class="ranking-number" v-if="player.id">
              {{ player.rank }}
            </div>
            <div class="empty-ranking-number" v-else></div>
            <div class="ranking-avatar">
              <avatar-display
                v-if="player.avatarFileId"
                :avatarFileId="player.avatarFileId"
                style="width: 40px; height: 40px"
                size="40px"
              />
              <span v-else class="iconfont teacup-icon">&#xe878;</span>
            </div>
            <div class="ranking-name">{{ player.name }}</div>
            <div
              class="ranking-score"
              :class="{
                positive: player.score >= 0,
                negative: player.score < 0,
              }"
            >
              {{
                player.score > 0
                  ? "+ " + player.score.toFixed(2)
                  : "- " + Math.abs(player.score).toFixed(2)
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  name: "RoomRanking",
  components: {
    AvatarDisplay,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    players: {
      type: Array,
      default: () => [],
    },
    teaWaterBalance: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    sortedRankingList() {
      // 先获取排序后的玩家列表
      const sortedPlayers = [...this.players].sort((a, b) => b.score - a.score);

      // 添加玩家索引作为排名
      const playersWithRank = sortedPlayers.map((player, index) => ({
        ...player,
        rank: index + 1, // 排名从1开始
      }));

      // 创建一个包含玩家和茶水的完整排名列表
      return [
        // 添加茶水项
        {
          name: "茶水",
          score: this.teaWaterBalance,
        },
        // 添加所有已排序的玩家
        ...playersWithRank,
      ];
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 房间排行榜弹窗样式 */
.ranking-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
}

.ranking-modal-content {
  @include modal-content;
  background-color: $card-background;
  max-height: 70vh;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;

  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.ranking-list-container {
  overflow-y: auto;
  max-height: calc(70vh - 60px);
  padding: 0 $spacing-lg;
}

.ranking-list {
  width: 100%;
}

.ranking-item {
  @include list-item;
  padding: $spacing-md 0;
}

.ranking-number {
  @include icon-container(32px, $primary-light);
  color: $primary-color;
  border-radius: $border-radius-round;
  margin-right: $spacing-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
}

.empty-ranking-number {
  width: 32px;
  margin-right: $spacing-md;
}

.ranking-avatar {
  @include avatar(40px);
  margin-right: $spacing-md;

  .teacup-icon {
    font-size: 24px;
    color: $warning-color;
  }
}

.ranking-name {
  flex: 1;
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.ranking-score {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;

  &.positive {
    color: $positive-amount-color;
  }

  &.negative {
    color: $negative-amount-color;
  }
}
</style>
