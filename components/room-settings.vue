<template>
  <div class="settings-modal" v-if="visible">
    <div class="settings-modal-content">
      <div class="modal-header">
        <div class="modal-title">设置</div>
        <div class="modal-close" @click="handleClose">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>
      <div class="settings-list">
        <div class="settings-item">
          <div class="settings-name">语音播报</div>
          <div
            class="toggle-switch"
            :class="{ active: soundEnabled }"
            @click="toggleSound"
          >
            <div class="toggle-switch-handle"></div>
          </div>
        </div>
        <div class="settings-item">
          <div class="settings-name">屏幕常亮</div>
          <div
            class="toggle-switch"
            :class="{ active: screenWakeLockEnabled }"
            @click="toggleScreenWakeLock"
          >
            <div class="toggle-switch-handle"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "RoomSettings",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    soundEnabled: {
      type: Boolean,
      default: true,
    },
    screenWakeLockEnabled: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    toggleSound() {
      this.$emit("toggle-sound");
    },
    toggleScreenWakeLock() {
      this.$emit("toggle-screen-wake-lock");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 房间设置弹窗样式 */
.settings-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: center;
}

.settings-modal-content {
  @include modal-content;
  width: 80%;
  max-width: 320px;
  background-color: $card-background;
  border-radius: $border-radius-lg;
  max-height: none;
  border: 1px solid $border-light;
  box-shadow: $box-shadow-lg;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.settings-list {
  width: 100%;
  padding: $spacing-md 0;
  min-height: 120px;
}

.settings-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
}

.settings-name {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.toggle-switch {
  width: 50px;
  height: 30px;
  background-color: $border-color;
  border-radius: 15px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &.active {
    background-color: $primary-color;
  }

  &:hover {
    opacity: 0.8;
  }
}

.toggle-switch-handle {
  width: 26px;
  height: 26px;
  border-radius: 13px;
  background-color: $text-white;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active .toggle-switch-handle {
  transform: translateX(20px);
}
</style>
