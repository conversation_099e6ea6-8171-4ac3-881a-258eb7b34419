<template>
  <div class="download-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-icon">
        <span class="iconfont biaopai-icon">&#xe87e;</span>
      </div>
      <div class="header-content">
        <div class="header-title">桌面标牌</div>
        <div class="header-subtitle">下载打印标牌，方便顾客扫码进入</div>
      </div>
    </div>

    <!-- 使用说明区域 -->
    <div class="instruction-section">
      <div class="section-title">
        <span class="iconfont instruction-icon">&#xe884;</span>
        使用说明
      </div>
      <div class="instruction-content">
        <div class="instruction-item">
          <div class="step-number">1</div>
          <div class="step-text">下载标识图片到手机相册</div>
        </div>
        <div class="instruction-item">
          <div class="step-number">2</div>
          <div class="step-text">使用打印店设备打印标识</div>
        </div>
        <div class="instruction-item">
          <div class="step-number">3</div>
          <div class="step-text">张贴在茶馆醒目位置</div>
        </div>
        <div class="instruction-item">
          <div class="step-number">4</div>
          <div class="step-text">顾客可扫码直接进入记分</div>
        </div>
      </div>
    </div>

    <!-- 二维码预览区域 -->
    <div class="qr-preview-section">
      <div class="section-title">
        <span class="iconfont qr-icon">&#xe87c;</span>
        标牌预览
      </div>
      <div class="qr-container">
        <div class="qr-card">
          <div class="qr-header">
            <div class="qr-title">打牌记账</div>
            <div class="qr-slogan">轻松记分，快乐游戏</div>
          </div>
          <div class="qr-code-area">
            <image class="qr-image" :src="filePath" mode="aspectFit" />
            <div class="qr-hint">请使用微信扫一扫</div>
          </div>
          <div class="qr-footer">
            <div class="feature-list">
              <div class="feature-item">• 多人同时记分</div>
              <div class="feature-item">• 自动计算输赢</div>
              <div class="feature-item">• 历史记录查看</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="download-tips">
        <span class="iconfont tip-icon">&#xe87d;</span>
        <span class="tip-text">建议打印尺寸：A4纸张，确保二维码清晰可扫</span>
      </div>
      <button class="download-btn" @click="downloadImage">
        <span class="download-text">下载标识图片</span>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      filePath:
        "https://env-00jxtnwq7mkk.normal.cloudstatic.cn/help/qr_code.jpg",
    };
  },
  methods: {
    downloadImage() {
      uni.showLoading({
        title: "正在保存...",
      });
      uni.downloadFile({
        url: this.filePath,
        success: (res) => {
          console.log(res, "res");
          // 检查状态码（部分端需要）
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: function () {
                uni.hideLoading();
                uni.showToast({
                  title: "保存成功",
                  icon: "success",
                });
              },
              fail: function (err) {
                console.error(err);
                uni.hideLoading();
                uni.showModal({
                  title: "保存失败",
                  content: "请检查是否授权访问相册，或稍后重试",
                  showCancel: false,
                });
              },
            });
          } else {
            uni.hideLoading();
            uni.showModal({
              title: "下载失败",
              content: "图片下载失败，请稍后重试",
              showCancel: false,
            });
          }
        },
        fail: (err) => {
          console.error(err);
          uni.hideLoading();
          uni.showModal({
            title: "下载失败",
            content: "图片下载失败，请检查网络或稍后重试",
            showCancel: false,
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.download-page {
  @include page-container;
  padding-bottom: $spacing-xl;
}

.page-header {
  @include card;
  padding: $spacing-xl $spacing-lg;
  margin-bottom: $spacing-base;
  display: flex;
  align-items: center;
  gap: $spacing-md;

  .header-icon {
    @include icon-container(50px, $primary-light);
    font-size: 24px;
    color: $primary-color;
    flex-shrink: 0;
  }

  .header-content {
    flex: 1;

    .header-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-xs;
    }

    .header-subtitle {
      font-size: $font-size-sm;
      color: $text-muted;
      line-height: 1.4;
    }
  }
}

.instruction-section {
  @include card;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin-bottom: $spacing-md;

    .instruction-icon {
      color: $secondary-color;
      font-size: $font-size-lg;
    }
  }

  .instruction-content {
    .instruction-item {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      padding: $spacing-sm 0;

      .step-number {
        @include icon-container(28px, $primary-color);
        font-size: $font-size-sm;
        font-weight: $font-weight-semibold;
        color: $text-white;
        border-radius: $border-radius-round;
        flex-shrink: 0;
      }

      .step-text {
        font-size: $font-size-base;
        color: $text-secondary;
        line-height: 1.5;
      }
    }
  }
}

.qr-preview-section {
  @include card;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin-bottom: $spacing-lg;

    .qr-icon {
      color: $success-color;
      font-size: $font-size-lg;
    }
  }

  .qr-container {
    display: flex;
    justify-content: center;

    .qr-card {
      background: linear-gradient(
        135deg,
        $primary-color 0%,
        $primary-dark 100%
      );
      width: 100%;
      max-width: 320px;
      padding: $spacing-xl;
      border-radius: $border-radius-lg;
      color: $text-white;
      box-shadow: $box-shadow-lg;

      .qr-header {
        text-align: center;
        margin-bottom: $spacing-lg;

        .qr-title {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-xs;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .qr-slogan {
          font-size: $font-size-sm;
          opacity: 0.9;
        }
      }

      .qr-code-area {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: $spacing-lg;

        .qr-image {
          width: 180px;
          height: 180px;
          background-color: $text-white;
          border-radius: $border-radius-base;
          padding: $spacing-sm;
          margin-bottom: $spacing-md;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .qr-hint {
          font-size: $font-size-sm;
          opacity: 0.9;
          text-align: center;
        }
      }

      .qr-footer {
        .feature-list {
          .feature-item {
            font-size: $font-size-sm;
            opacity: 0.9;
            margin-bottom: $spacing-xs;
            padding-left: $spacing-sm;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.action-section {
  @include card;
  padding: $spacing-lg;

  .download-tips {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    padding: $spacing-md;
    background-color: rgba(7, 193, 96, 0.1);
    border: 1px solid rgba(7, 193, 96, 0.2);
    border-radius: $border-radius-base;
    margin-bottom: $spacing-lg;

    .tip-icon {
      color: $success-color;
      font-size: $font-size-md;
      flex-shrink: 0;
    }

    .tip-text {
      font-size: $font-size-sm;
      color: $success-color;
      line-height: 1.4;
    }
  }

  .download-btn {
    @include button-primary;
    width: 100%;
    padding: $spacing-md $spacing-lg;
    gap: $spacing-sm;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;

    .download-text {
      flex: 1;
    }

    &:active {
      background-color: $primary-dark;
    }
  }
}
</style>
