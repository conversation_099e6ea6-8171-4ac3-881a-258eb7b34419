// 房间数据管理 Mixin
// 负责处理房间初始化、数据加载和玩家列表管理

import { RoomService } from "@/utils/room.js";
import { showToast } from "@/utils/commonMethods.js";
import { safeAdd } from "@/utils/mathUtils.js";

export default {
  data() {
    return {
      // 房间基础数据
      roomInfo: {},
      players: [],
      currentRound: 1,
      maxPlayers: 8,

      // 数据加载状态
      isRoomDataLoading: false,
      roomDataLoadError: null,

      // 游戏基础配置
      gameStyle: "classic",
      tableScore: 0,

      // 局状态管理数据
      roundStats: {
        currentRound: 1,
        tableScore: 0,
        playerStats: {},
        roundHistory: [],
      },
    };
  },

  computed: {
    // 是否可以邀请更多玩家
    canInviteMore() {
      return this.players.length < this.maxPlayers;
    },
  },

  methods: {
    // ==================== 房间初始化方法 ====================

    /**
     * 初始化房间
     * @param {Object} options 页面参数
     */
    async initializeRoom(options) {
      console.log(options, "options");
      try {
        // 支持 gameStyle 和 gameType 两种参数名（兼容性处理）
        const gameType = options.gameStyle || options.gameType;
        if (gameType) {
          this.gameStyle = gameType;
          this.currentView = gameType === "classic" ? "chat" : "table";
        }

        // 获取房间ID（支持多种方式传递）
        const roomId = options.roomId || options.room_id || options.id;

        // 检查房间ID是否存在
        if (!roomId) {
          throw new Error("房间ID不能为空，请通过正确的方式进入房间");
        }

        // 先初始化用户信息，确保用户ID可用
        await this.initializeUserProfile();

        // 再加载真实房间数据
        await this.loadRealRoomData(roomId);
      } catch (error) {
        console.error("初始化房间数据失败:", error);

        // 根据错误类型显示不同的提示信息
        let errorMessage = "加载房间数据失败";
        if (error.message.includes("房间ID不能为空")) {
          errorMessage = "房间参数错误，请重新进入";
        } else if (error.message.includes("房间不存在")) {
          errorMessage = "房间不存在或已结束";
        } else if (error.message.includes("用户未登录")) {
          errorMessage = "登录状态异常，请重新登录";
        } else if (error.message.includes("无权限")) {
          errorMessage = "无权限访问该房间";
        } else {
          errorMessage = error.message || "网络异常，请稍后重试";
        }

        showToast(errorMessage, "none");

        // 延时返回上一页，给用户时间看到错误信息
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      }
    },

    /**
     * 加载真实房间数据
     * @param {string} roomId 房间ID
     */
    async loadRealRoomData(roomId) {
      try {
        console.log("正在加载真实房间数据:", roomId);

        // 设置加载状态
        this.isRoomDataLoading = true;
        this.roomDataLoadError = null;

        // 获取房间信息
        const roomResult = await RoomService.getRoomInfo(roomId);

        if (!roomResult.success) {
          // 根据错误码提供更准确的错误信息
          let errorMsg = roomResult.message || "获取房间信息失败";
          if (roomResult.code === 404) {
            errorMsg = "房间不存在";
          } else if (roomResult.code === 403) {
            errorMsg = "无权限访问该房间";
          } else if (roomResult.code === 401) {
            errorMsg = "用户未登录，请重新登录";
          }
          throw new Error(errorMsg);
        }

        const roomData = roomResult.data;
        console.log(roomData, "roomData");

        // 获取玩家列表
        const playersResult = await RoomService.getRoomPlayers(roomId);

        if (!playersResult.success) {
          throw new Error(playersResult.message || "获取玩家列表失败");
        }

        // 更新房间信息
        this.roomInfo = {
          name: roomData.roomName,
          id: roomData.roomId,
        };

        // 更新玩家列表
        this.players = playersResult.data;
        console.log(this.players, "this.players");

        // 根据从数据库获取的房间类型设置正确的视图（数据库数据为准）
        if (roomData.gameType) {
          this.gameStyle = roomData.gameType;
          this.currentView = roomData.gameType === "classic" ? "chat" : "table";
          console.log(
            `房间类型: ${roomData.gameType}, 视图类型: ${this.currentView}`
          );
        }

        // 更新其他游戏状态
        this.currentRound = roomData.gameRounds || 1;
        this.maxPlayers = roomData.maxPlayers || 8;

        // 初始化局状态管理
        this.initializeRoundStats();

        // 加载茶水设置 - 通过事件通知其他mixin
        const teaSettings = {
          teaWaterLimitAmount: roomData.teaWaterLimitAmount || null,
          teaWaterRatio: roomData.teaWaterRatio || 0,
          teaWaterBalance: roomData.teaWaterBalance || 0.0,
        };
        this.handleTeaSettingsLoaded(teaSettings);

        // 初始化其他记录数据（在加载消息前先初始化）
        this.playerRecords = {};
        this.playerTransactions = {};

        // 加载真实的房间消息数据（这会重新计算个人流水和桌面分数）
        if (this.loadRoomMessages) {
          await this.loadRoomMessages();
        }

        // 注意：桌面分数已通过 loadRoomMessages() 中的 updateCurrentRoundFromMessages() 正确计算
        // 不需要手动重置为0，以保持数据一致性（仅给分玩法）

        this.isRoomDataLoading = false;
        console.log("真实房间数据加载成功");

        // 房间数据加载完成后，初始化WebSocket连接
        if (this.initWebSocket) {
          try {
            await this.initWebSocket(roomId);
          } catch (wsError) {
            console.warn("WebSocket连接初始化失败，将使用普通模式:", wsError);
          }
        }
      } catch (error) {
        this.isRoomDataLoading = false;
        this.roomDataLoadError = error.message || "加载房间数据失败";
        console.error("加载真实房间数据失败:", error);
        throw error;
      }
    },

    /**
     * 刷新玩家列表
     * 用于实时更新房间内的玩家信息
     */
    async refreshPlayersList() {
      try {
        const roomId = this.roomInfo?.id;
        if (!roomId) {
          console.warn("房间ID不存在，无法刷新玩家列表");
          return;
        }

        console.log("正在刷新玩家列表...");

        const result = await RoomService.getRoomPlayers(roomId);

        if (result.success) {
          this.players = result.data;
          console.log("玩家列表刷新成功，当前玩家数量:", this.players.length);

          // 玩家列表更新后，如果已有消息数据，重新计算相关数据
          if (this.messageData && this.messageData.length > 0) {
            if (this.calculatePlayersScoreFromMessages) {
              this.calculatePlayersScoreFromMessages();
            }

            // 传统玩法需要重新计算当前用户流水
            if (this.isTraditionalView) {
              if (this.calculateCurrentUserTransactions) {
                this.calculateCurrentUserTransactions();
              }
            } else {
              // 给分玩法才需要计算所有玩家的流水和记录
              if (this.calculateAllPlayersTransactions) {
                this.calculateAllPlayersTransactions();
              }
              if (this.calculateAllPlayersRecords) {
                this.calculateAllPlayersRecords();
              }
            }
          }
        } else {
          console.error("刷新玩家列表失败--:", result.message);
          // 不显示错误提示，避免频繁打扰用户
        }
      } catch (error) {
        console.error("刷新玩家列表异常:", error);
      }
    },

    /**
     * 用户信息更新事件处理
     */
    async onUserInfoUpdated(userInfo) {
      console.log("房间页面 - 用户信息已更新:", userInfo);
      
      // 确保房间数据已加载完成
      if (!this.roomInfo?.id || this.isRoomDataLoading) {
        console.log("房间数据未就绪，跳过玩家列表刷新");
        return;
      }
      
      try {
        await this.refreshPlayersList();
        console.log("用户信息更新后，房间玩家列表已刷新");
      } catch (error) {
        console.warn("刷新房间玩家列表失败:", error);
      }
    },

    /**
     * 获取当前用户对象
     */
    getCurrentUser() {
      if (!this.realCurrentUserId) return null;
      return this.players.find((p) => p.id === this.realCurrentUserId);
    },

    /**
     * 更新玩家分数
     * @param {string} playerId 玩家ID
     * @param {number} scoreChange 分数变化
     */
    updatePlayerScore(playerId, scoreChange) {
      const playerIndex = this.players.findIndex((p) => p.id === playerId);
      if (playerIndex !== -1) {
        this.players[playerIndex].score = safeAdd(
          this.players[playerIndex].score,
          scoreChange
        );
      }
    },

    /**
     * 清理房间相关的本地数据
     */
    clearRoomData() {
      // 重置房间数据（可选，因为即将离开页面）
      this.players = [];
      this.roomInfo = {};
      this.currentRound = 1;
      this.tableScore = 0;
      this.isRoomDataLoading = false;
      this.roomDataLoadError = null;

      // 重置局状态数据
      this.roundStats = {
        currentRound: 1,
        tableScore: 0,
        playerStats: {},
        roundHistory: [],
      };

      // 清理WebSocket相关配置，防止页面切换时误重连
      if (this.wsConfig) {
        this.wsConfig.roomId = '';
        this.wsConfig.token = '';
        console.log('WebSocket配置已清理');
      }

      // 清理其他数据
      if (this.messageData) {
        this.messageData = [];
      }
      if (this.playerRecords) {
        this.playerRecords = {};
      }
      if (this.playerTransactions) {
        this.playerTransactions = {};
      }
    },

    /**
     * 初始化局状态
     * @returns {void}
     */
    initializeRoundStats() {
      try {
        // 传统玩法不需要局状态管理
        if (this.isTraditionalView) {
          console.log("传统玩法跳过局状态初始化");
          return;
        }

        this.roundStats = {
          currentRound: 1,
          tableScore: 0,
          playerStats: {},
          roundHistory: [],
        };

        // 为所有玩家初始化统计数据
        if (this.players && this.players.length > 0) {
          this.players.forEach((player) => {
            this.roundStats.playerStats[player.id] = {
              distributeCount: 0,
              collectCount: 0,
            };
          });
        }

        console.log("局状态初始化完成");
      } catch (error) {
        console.error("初始化局状态失败:", error);
      }
    },

    /**
     * 获取玩家局内统计显示文本
     * @param {Object} player - 玩家对象
     * @returns {Object} 显示文本对象
     */
    getPlayerRoundStats(player) {
      try {
        // 传统玩法不需要局内统计
        if (this.isTraditionalView) {
          return {
            distributeText: "传统记分",
            collectText: "累计分数",
          };
        }

        if (!player || !player.id) {
          return {
            distributeText: "未出分",
            collectText: "未收分",
          };
        }

        // 使用messageManageMixin中的方法计算实时统计
        const stats = this.calculatePlayerRoundStats
          ? this.calculatePlayerRoundStats(player.id, this.currentRound)
          : { distributeCount: 0, collectCount: 0 };

        const distributeText =
          stats.distributeCount > 0
            ? `出分${stats.distributeCount}次`
            : "未出分";
        const collectText =
          stats.collectCount > 0 ? `收分${stats.collectCount}次` : "未收分";

        return {
          distributeText,
          collectText,
        };
      } catch (error) {
        console.error("获取玩家局内统计失败:", error);
        return {
          distributeText: "未出分",
          collectText: "未收分",
        };
      }
    },
  },
};
