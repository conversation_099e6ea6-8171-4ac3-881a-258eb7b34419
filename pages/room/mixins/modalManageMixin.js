// 弹窗管理 Mixin
// 负责统一管理所有弹窗的显示/隐藏状态

import {
  formatToLocaleString,
  now,
  formatTimestamp,
} from "@/utils/dateUtils.js";

export default {
  data() {
    return {
      // 弹窗状态管理
      modalStates: {
        showRankingModal: false,
        showSettingsModal: false,
        showInviteModal: false,
        showProfileEditorModal: false,
        showTeaModal: false,
        showDistributeModal: false,
        showCollectModal: false,
        showTableRecordsModal: false,
        showPlayerRecordsModal: false,
        showPlayerTransactionsModal: false,
      },

      // 记录相关数据
      playerRecords: {},
      playerTransactions: {},

      // 选中的玩家数据
      selectedPlayerForRecords: null,
      selectedPlayerForTransactions: null,
    };
  },

  computed: {
    // ==================== 弹窗状态计算属性 ====================

    // 排行榜弹窗显示状态
    showRankingModal: {
      get() {
        return this.modalStates.showRankingModal;
      },
      set(value) {
        this.modalStates.showRankingModal = value;
      },
    },

    // 设置弹窗显示状态
    showSettingsModal: {
      get() {
        return this.modalStates.showSettingsModal;
      },
      set(value) {
        this.modalStates.showSettingsModal = value;
      },
    },

    // 邀请好友弹窗显示状态
    showInviteModal: {
      get() {
        return this.modalStates.showInviteModal;
      },
      set(value) {
        this.modalStates.showInviteModal = value;
      },
    },

    // 用户资料编辑弹窗显示状态
    showProfileEditorModal: {
      get() {
        return this.modalStates.showProfileEditorModal;
      },
      set(value) {
        this.modalStates.showProfileEditorModal = value;
      },
    },

    // 茶水设置弹窗显示状态
    showTeaModal: {
      get() {
        return this.modalStates.showTeaModal;
      },
      set(value) {
        this.modalStates.showTeaModal = value;
      },
    },

    // 出分弹窗显示状态
    showDistributeModal: {
      get() {
        return this.modalStates.showDistributeModal;
      },
      set(value) {
        this.modalStates.showDistributeModal = value;
      },
    },

    // 收分弹窗显示状态
    showCollectModal: {
      get() {
        return this.modalStates.showCollectModal;
      },
      set(value) {
        this.modalStates.showCollectModal = value;
      },
    },

    // 桌面记录弹窗显示状态
    showTableRecordsModal: {
      get() {
        return this.modalStates.showTableRecordsModal;
      },
      set(value) {
        this.modalStates.showTableRecordsModal = value;
      },
    },

    // 个人记录弹窗显示状态
    showPlayerRecordsModal: {
      get() {
        return this.modalStates.showPlayerRecordsModal;
      },
      set(value) {
        this.modalStates.showPlayerRecordsModal = value;
      },
    },

    // 个人流水弹窗显示状态
    showPlayerTransactionsModal: {
      get() {
        return this.modalStates.showPlayerTransactionsModal;
      },
      set(value) {
        this.modalStates.showPlayerTransactionsModal = value;
      },
    },

    /**
     * 动态计算可用的局数列表
     * 从消息数据中提取所有出现过的局数
     */
    availableRounds() {
      try {
        if (!this.messageData || this.messageData.length === 0) {
          // 没有消息数据时，返回当前局数或默认第1局
          return [this.currentRound || 1];
        }

        // 从消息中收集所有的局数
        const roundsSet = new Set();

        this.messageData.forEach((message) => {
          // 检查消息是否有局数信息
          if (
            message.round_number &&
            typeof message.round_number === "number"
          ) {
            roundsSet.add(message.round_number);
          }
          // 兼容详细数据中的局数
          if (
            message.detail_data &&
            message.detail_data.round_number &&
            typeof message.detail_data.round_number === "number"
          ) {
            roundsSet.add(message.detail_data.round_number);
          }
        });

        // 如果没有找到任何局数，返回当前局数
        if (roundsSet.size === 0) {
          return [this.currentRound || 1];
        }

        // 转换为数组并排序
        const rounds = Array.from(roundsSet).sort((a, b) => a - b);

        // 确保当前局数也在列表中
        if (this.currentRound && !rounds.includes(this.currentRound)) {
          rounds.push(this.currentRound);
          rounds.sort((a, b) => a - b);
        }

        console.log("计算得到的可用局数列表:", rounds);
        return rounds;
      } catch (error) {
        console.error("计算可用局数列表失败:", error);
        // 出错时返回当前局数或默认第1局
        return [this.currentRound || 1];
      }
    },

    /**
     * 动态计算桌面记录
     * 从消息数据中提取出分和收分记录，按局数分组
     */
    tableRecords() {
      try {
        // 传统玩法不需要桌面记录
        if (this.isTraditionalView) {
          return {};
        }
        
        if (!this.messageData || this.messageData.length === 0) {
          return {};
        }

        const records = {};

        this.messageData.forEach((message) => {
          // 只处理出分和收分消息
          if (!["distribute", "collect"].includes(message.type)) {
            return;
          }

          // 获取局数
          let roundNumber =
            message.round_number ||
            (message.detail_data && message.detail_data.round_number);

          if (!roundNumber || typeof roundNumber !== "number") {
            return;
          }

          // 确保该局的记录数组存在
          if (!records[roundNumber]) {
            records[roundNumber] = [];
          }

          // 根据消息类型创建记录
          let record = null;

          if (message.type === "distribute") {
            const amount =
              message.amount ||
              (message.detail_data && message.detail_data.amount);

            if (amount && typeof amount === "number") {
              record = {
                id:
                  message._id ||
                  message.id ||
                  `distribute_${Date.now()}_${Math.random()}`,
                timestamp: formatTimestamp(message.timestamp, "YYYY-MM-DD HH:mm:ss"),
                playerName:
                  message.sender_name ||
                  (message.sender && message.sender.name) ||
                  "未知玩家",
                action: "出分到桌面",
                amount: amount,
              };
            }
          } else if (message.type === "collect") {
            const originalAmount =
              message.original_amount ||
              (message.detail_data && message.detail_data.original_amount);

            if (originalAmount && typeof originalAmount === "number") {
              record = {
                id:
                  message._id ||
                  message.id ||
                  `collect_${Date.now()}_${Math.random()}`,
                timestamp: formatTimestamp(message.timestamp, "YYYY-MM-DD HH:mm:ss"),
                playerName:
                  message.sender_name ||
                  (message.sender && message.sender.name) ||
                  "未知玩家",
                action: "从桌面收分",
                amount: -originalAmount, // 收分显示为负数
              };
            }
          }

          if (record) {
            records[roundNumber].push(record);
          }
        });
        // 对每局的记录按时间排序（最新的在前）
        Object.keys(records).forEach((roundNumber) => {
          records[roundNumber].sort((a, b) => {
            return new Date(b.timestamp.replace(/-/g, '/')) - new Date(a.timestamp.replace(/-/g, '/'));
          });
        });

        console.log("计算得到的桌面记录:", records);
        return records;
      } catch (error) {
        console.error("计算桌面记录失败:", error);
        return {};
      }
    },
  },

  methods: {
    // ==================== 核心弹窗方法 ====================
    // 注意：这些方法被其他mixin调用，请勿重复定义
  },

  methods: {
    // ==================== 弹窗显示方法 ====================

    /**
     * 邀请好友
     */
    inviteFriends() {
      this.showInviteModal = true;
    },
    /*
     * 显示房间排行榜
     */
    showRoomRanking() {
      this.showRankingModal = true;
    },

    /**
     * 显示房间设置
     */
    showRoomSettings() {
      this.showSettingsModal = true;
    },

    /**
     * 收分
     */
    collectScore() {
      this.showCollectModal = true;
    },

    /**
     * 出分到桌面
     */
    distributeScore() {
      this.showDistributeModal = true;
    },

    /**
     * 查看桌面记录
     */
    viewRecords() {
      this.showTableRecordsModal = true;
    },

    /**
     * 显示玩家记录
     * @param {Object} player 玩家对象
     */
    showPlayerRecords(player) {
      this.selectedPlayerForRecords = player;
      this.showPlayerRecordsModal = true;
    },

    /**
     * 显示玩家流水
     * @param {Object} player 玩家对象
     */
    showPlayerTransactions(player) {
      this.selectedPlayerForTransactions = player;
      this.showPlayerTransactionsModal = true;
    },

    // ==================== 弹窗关闭方法 ====================

    /**
     * 关闭房间排行榜弹窗
     */
    closeRankingModal() {
      this.showRankingModal = false;
    },

    /**
     * 关闭房间设置弹窗
     */
    closeSettingsModal() {
      this.showSettingsModal = false;
    },

    /**
     * 关闭邀请好友弹窗
     */
    closeInviteModal() {
      this.showInviteModal = false;
    },

    /**
     * 关闭用户资料编辑弹窗
     */
    closeProfileEditor() {
      this.showProfileEditorModal = false;
    },

    /**
     * 关闭茶水设置弹窗
     */
    closeTeaSettings() {
      this.showTeaModal = false;
    },

    /**
     * 关闭出分弹窗
     */
    closeDistributeModal() {
      this.showDistributeModal = false;
    },

    /**
     * 关闭收分弹窗
     */
    closeCollectModal() {
      this.showCollectModal = false;
    },

    /**
     * 关闭桌面记录弹窗
     */
    closeTableRecordsModal() {
      this.showTableRecordsModal = false;
    },

    /**
     * 关闭个人记录弹窗
     */
    closePlayerRecordsModal() {
      this.showPlayerRecordsModal = false;
    },

    /**
     * 关闭个人流水弹窗
     */
    closePlayerTransactionsModal() {
      this.showPlayerTransactionsModal = false;
    },

    // ==================== 数据处理方法 ====================

    /**
     * 添加交易记录
     * @param {string} playerId 玩家ID
     * @param {string} type 交易类型
     * @param {string} description 描述
     * @param {number} amount 金额
     */
    addTransactionRecord(playerId, type, description, amount) {
      if (!this.playerTransactions[playerId]) {
        this.playerTransactions[playerId] = [];
      }

      const newTransaction = {
        id: now() + Math.random(),
        timestamp: formatToLocaleString(now(), {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        }),
        type,
        description,
        amount,
      };

      this.playerTransactions[playerId].push(newTransaction);
    },

    // ==================== 其他事件处理 ====================

    /**
     * 处理轮次变化
     * @param {number} selectedRound 选中的轮次
     */
    handleRoundChange(selectedRound) {
      this.selectedRound = selectedRound;
    },
  },
};
