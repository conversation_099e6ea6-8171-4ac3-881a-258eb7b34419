// UI辅助 Mixin
// 负责处理UI相关的工具方法、提示文本管理等

import { formatScoreDisplay } from "@/utils/commonMethods.js";

export default {
  data() {
    return {
      // 视图状态
      currentView: "chat", // 'chat' 或 'table'
      
      // 提示文本管理
      currentHintIndex: 0,
      hintScrollTimer: null,
    };
  },

  computed: {
    // 是否是传统记分视图
    isTraditionalView() {
      return this.currentView === "chat";
    },

    // 当前提示文本列表
    currentHints() {
      if (this.isTraditionalView) {
        return [
          "点击朋友头像可单独给指定朋友记录",
          "记录一笔支出可以为多位朋友记分",
          "点击生成结算查看当前游戏结果",
          "点击茶水按钮可以设置抽取比例",
        ];
      } else {
        return [
          "点击邀请好友可以邀请朋友加入房间",
          "点击出分到桌面将分数放到桌面",
          "点击收分可以从桌面收取分数",
          "点击玩家头像查看个人流水记录",
        ];
      }
    },

    // 底部功能按钮配置 - 在computed中定义以确保方法引用正确
    bottomButtons() {
      return [
        { name: "房间排行", icon: `\ue875`, action: this.showRoomRanking },
        { name: "房间设置", icon: `\ue871`, action: this.showRoomSettings },
        { name: "退出房间", icon: `\ue876`, action: this.exitRoom },
      ];
    },
  },

  watch: {
    // 监听视图切换，重新开始提示文本轮播
    currentView() {
      this.currentHintIndex = 0;
      this.startHintScrolling();
    },
  },

  mounted() {
    // 开始提示文本轮播
    this.startHintScrolling();
  },

  beforeDestroy() {
    // 停止提示文本轮播
    this.stopHintScrolling();
  },

  methods: {
    // 导入格式化分数显示的方法
    formatScoreDisplay,
    
    // ==================== 工具方法 ====================
    
    /**
     * 计算分数徽章的字体大小
     * @param {number} score 分数
     * @returns {string} 字体大小
     */
    getScoreFontSize(score) {
      const absScore = Math.abs(score);
      if (absScore >= 1000000) {
        return "8px";
      } else if (absScore >= 100000) {
        return "9px";
      } else if (absScore >= 10000) {
        return "9px";
      } else {
        return "10px";
      }
    },

    /**
     * 获取玩家头像的样式类
     * @param {Object} player 玩家对象
     * @returns {Object} 样式类对象
     */
    getPlayerAvatarClass(player) {
      return {
        "positive-bg": player.score > 0,
        "negative-bg": player.score < 0,
      };
    },

    /**
     * 获取分数徽章的样式类
     * @param {number} score 分数
     * @returns {string} 样式类名
     */
    getScoreBadgeClass(score) {
      return score >= 0 ? "positive" : "negative";
    },

    // ==================== 提示文本管理 ====================
    
    /**
     * 开始提示文本轮播
     */
    startHintScrolling() {
      this.stopHintScrolling(); // 先清除之前的定时器
      this.hintScrollTimer = setInterval(() => {
        this.currentHintIndex =
          (this.currentHintIndex + 1) % this.currentHints.length;
      }, 5000); // 每5秒切换一次
    },

    /**
     * 停止提示文本轮播
     */
    stopHintScrolling() {
      if (this.hintScrollTimer) {
        clearInterval(this.hintScrollTimer);
        this.hintScrollTimer = null;
      }
    },

    // ==================== 网络状态处理 ====================
    
    /**
     * 网络状态变化处理（从mixin触发）
     * @param {Object} networkInfo 网络信息
     */
    onNetworkStatusChanged(networkInfo) {
      console.log("房间页面 - 网络状态变化:", networkInfo);

      // 根据网络状态显示相应的提示
      if (networkInfo.status === "disconnected") {
        uni.showToast({
          title: "网络连接断开，请检查网络",
          icon: "none"
        });
      } else if (networkInfo.status === "poor") {
        uni.showToast({
          title: "网络信号较弱",
          icon: "none"
        });
      }

      // 可以在这里添加其他业务逻辑，如暂停游戏、保存数据等
    },
  }
}; 