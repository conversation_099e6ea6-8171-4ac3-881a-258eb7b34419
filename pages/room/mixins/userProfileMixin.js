// 用户信息管理 Mixin
// 负责处理当前用户的头像、昵称显示和修改功能

import loginFun from "@/utils/login.js";
import { showToast, handleError, validateUserInfo } from "@/utils/commonMethods.js";

export default {
  data() {
    return {
      // 当前用户信息 - 简化数据结构，去除冗余字段
      currentUserProfile: {
        _id: null,
        wx_openid: null,
        nickname: '',
        avatar: '',
        signature: '',
        gender: 0,
      },
      
      // 用户数据加载状态
      isUserDataLoaded: false,
      isUserProfileLoading: false,
      
      // 默认头像
      defaultUserAvatar: "https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/default-avatar.jpg",
    };
  },

  computed: {
    // 当前用户是否已登录
    isUserLoggedIn() {
      return loginFun.checkLoginStatus() && this.currentUserProfile._id !== null;
    },
    
    // 获取真实的当前用户ID
    realCurrentUserId() {
      return this.currentUserProfile._id;
    },
    
    // 当前用户显示用的头像
    currentUserAvatar() {
      return this.currentUserProfile.avatar_fileId || this.defaultUserAvatar;
    },
    
    // 当前用户显示用的昵称
    currentUserNickname() {
      return this.currentUserProfile.nickname || '未设置昵称';
    },
    
    // 兼容profile editor组件的数据格式
    currentUserData() {
      return {
        username: this.currentUserProfile.nickname,
        avatar_fileId: this.currentUserProfile.avatar_fileId,
        signature: this.currentUserProfile.signature,
        gender: this.currentUserProfile.gender,
      };
    }
  },

  methods: {
    // ==================== 用户信息初始化 ====================
    
    /**
     * 初始化用户信息
     * 在页面加载时调用，获取当前登录用户信息
     */
    async initializeUserProfile() {
      if (this.isUserProfileLoading) return;
      
      this.isUserProfileLoading = true;
      
      try {
        // 检查登录状态
        if (!loginFun.checkLoginStatus()) {
          console.log('用户未登录，尝试自动登录...');
          await this.performAutoLogin();
          return;
        }

        // 已登录，获取用户信息
        await this.refreshCurrentUserProfile();
        
      } catch (error) {
        console.error('初始化用户信息失败:', error);
        // 初始化失败时使用默认信息
        this.setDefaultUserProfile();
      } finally {
        this.isUserProfileLoading = false;
      }
    },

    /**
     * 执行自动登录
     */
    async performAutoLogin() {
      try {
        const loginResult = await loginFun.login();
        
        if (loginResult.success) {
          const { userInfo, isNewUser } = loginResult.data;
          
          // 更新用户信息
          this.updateUserProfileFromCloud(userInfo);
          
          if (isNewUser) {
            console.log('检测到新用户');
            // 新用户可以选择是否显示编辑资料弹窗
            // 这里暂时不自动弹出，由调用方决定
          }
          
        } else {
          console.log('自动登录失败:', loginResult.message);
          this.setDefaultUserProfile();
        }
      } catch (error) {
        console.error('自动登录异常:', error);
        this.setDefaultUserProfile();
      }
    },

    /**
     * 刷新当前用户信息
     * 从云端获取最新的用户信息
     */
    async refreshCurrentUserProfile() {
      try {
        // 优先使用本地缓存的用户信息
        const localUserInfo = loginFun.storage.getUserInfo();
        if (localUserInfo && validateUserInfo(localUserInfo)) {
          this.updateUserProfileFromCloud(localUserInfo);
          
          // 后台刷新云端信息
          this.refreshFromCloudInBackground();
          return;
        }

        // 本地没有有效信息，从云端获取
        const result = await loginFun.getUserInfo();
        if (result.success) {
          this.updateUserProfileFromCloud(result.data);
        } else {
          throw new Error(result.message || '获取用户信息失败');
        }
        
      } catch (error) {
        console.error('刷新用户信息失败:', error);
        // 保持当前信息不变，避免清空已有数据
      }
    },

    /**
     * 后台刷新云端用户信息
     */
    async refreshFromCloudInBackground() {
      try {
        const result = await loginFun.getUserInfo();
        if (result.success) {
          // 静默更新用户信息
          this.updateUserProfileFromCloud(result.data, false);
        }
      } catch (error) {
        console.warn('后台刷新用户信息失败:', error);
      }
    },

    // ==================== 用户数据管理 ====================

    /**
     * 从云端数据更新本地用户信息
     * @param {Object} cloudUserData - 云端用户数据
     * @param {Boolean} updatePlayersList - 是否同步更新玩家列表
     */
    updateUserProfileFromCloud(cloudUserData, updatePlayersList = true) {
      if (!cloudUserData) return;

      // 更新用户信息 - 简化数据结构
      this.currentUserProfile = {
        _id: cloudUserData._id,
        wx_openid: cloudUserData.wx_openid,
        nickname: cloudUserData.nickname || '未设置昵称',
        avatar_fileId: cloudUserData.avatar_fileId || "",
        signature: cloudUserData.signature || '',
        gender: cloudUserData.gender || 0,
      };

      this.isUserDataLoaded = true;

      console.log('用户信息已更新:', this.currentUserProfile);

      // 通知用户信息更新事件
      this.notifyUserInfoUpdated();
    },

    /**
     * 通知用户信息更新事件
     */
    notifyUserInfoUpdated() {
      // 触发用户信息更新事件，让使用方决定如何处理
      if (this.$emit) {
        this.$emit('user-info-updated', this.currentUserProfile);
      }
      
      // 兼容非组件环境，通过全局事件总线
      if (typeof uni !== 'undefined' && uni.$emit) {
        uni.$emit('user-info-updated', this.currentUserProfile);
      }
    },

    /**
     * 设置默认用户信息
     */
    setDefaultUserProfile() {
      this.currentUserProfile = {
        _id: null,
        wx_openid: null,
        nickname: '游客用户',
        avatar: this.defaultUserAvatar,
        signature: '',
        gender: 0,
      };
      
      this.isUserDataLoaded = true;
      console.log('已设置默认用户信息');
    },



    /**
     * 从玩家列表获取当前用户 - 重命名以避免与主页面冲突
     */
    getCurrentUser() {
      if (!this.players || !this.realCurrentUserId) return null;
      return this.players.find(p => p.id === this.realCurrentUserId);
    },

    // ==================== 用户资料编辑 ====================

    /**
     * 显示用户资料编辑弹窗
     */
    showUserProfileEditor() {
      if (!this.isUserDataLoaded) {
        showToast('用户信息加载中，请稍后再试', 'none');
        return;
      }

      this.showProfileEditorModal = true;
    },

    /**
     * 处理用户资料保存
     * @param {Object} userData - 用户资料数据
     */
    async handleUserProfileSave(userData) {
      try {
        uni.showLoading({ title: '保存中...' });

        // 检查登录状态
        if (!loginFun.checkLoginStatus()) {
          throw new Error('用户未登录，请先登录');
        }

        // 更新云端用户信息
        const updateResult = await loginFun.updateUserInfo({
          nickname: userData.username,
          avatar_fileId: userData.avatar_fileId,
          signature: userData.signature,
          gender: userData.gender,
        });

        if (updateResult.success) {
          // 更新成功，使用返回的数据更新本地Profile
          if (updateResult.data) {
             this.updateUserProfileFromCloud(updateResult.data);
          }
          
          showToast('个人资料已更新', 'success');
          
          // 关闭编辑弹窗
          this.showProfileEditorModal = false;
          
        } else {
          throw new Error(updateResult.message || '保存失败');
        }
        
      } catch (error) {
        handleError(error, '保存失败，请重试');
      } finally {
        uni.hideLoading();
      }
    },

    // ==================== 用户操作 ====================

    /**
     * 显示当前用户操作菜单
     */
    showCurrentUserActionSheet() {
      if (!this.isUserDataLoaded) {
        showToast('用户信息加载中', 'none');
        return;
      }

      const actionItems = ['修改头像和昵称', '查看我的流水'];
      const actionHandlers = [
        this.showUserProfileEditor,
        this.viewCurrentUserTransactions,
      ];

      uni.showActionSheet({
        itemList: actionItems,
        success: (res) => {
          actionHandlers[res.tapIndex]?.();
        },
        fail: (err) => console.error('Action sheet error:', err),
      });
    },



    // ==================== 辅助方法 ====================

    /**
     * 检查是否为当前用户
     * @param {Object} player - 玩家对象
     */
    isCurrentUser(player) {
      return player && player.id === this.realCurrentUserId;
    },
  },

  // 生命周期钩子
  async created() {
    // mixin创建时不自动初始化，由使用方调用
  },
}; 