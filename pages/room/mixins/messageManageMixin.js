// 房间消息管理 Mixin
// 负责处理房间消息的增删改查和玩家分数计算

import { RoomService } from "@/utils/room.js";
import { compareTimestamps, formatTimestamp } from "@/utils/dateUtils.js";
import { parseSettlementAdjustments } from "@/utils/settlementUtils.js";
import { safeAdd, safeMinus } from "@/utils/mathUtils.js";

export default {
  data() {
    return {
      // 消息数据
      messageData: [],

      // 滚动相关
      scrollTop: 0,
      scrollUpdateCounter: 0,
      messagesScrollViewId: "messages-scroll-view",
    };
  },

  watch: {
    /**
     * 监听消息数据变化，自动滚动到底部
     * 仅在传统记分视图且有新消息时触发
     */
    messageData(newData, oldData) {
      try {
        // 检查基本条件
        if (!newData || !Array.isArray(newData)) {
          return;
        }

        // 检查是否为新增消息（数组长度增加）
        const isNewMessage =
          oldData && Array.isArray(oldData) && newData.length > oldData.length;

        // 只在有新消息且当前是传统记分视图时自动滚动
        if (
          isNewMessage &&
          this.isTraditionalView &&
          typeof this.scrollToBottom === "function"
        ) {
          console.log("检测到新消息，自动滚动到底部");

          // 延时执行滚动，确保DOM更新完成
          setTimeout(() => {
            this.scrollToBottom();
          }, 200);
        }
      } catch (error) {
        console.error("自动滚动异常:", error);
        // 滚动失败不影响消息功能，静默处理
      }
    },
  },

  methods: {
    // ==================== 房间消息管理 ====================

    /**
     * 添加房间消息
     * @param {string} messageType 消息类型：score/system/settlement
     * @param {Object} detailData 消息详细数据
     * @returns {Promise<Object>} 操作结果
     */
    async addRoomMessage(messageType, detailData) {
      try {
        const roomId = this.roomInfo?.id;
        if (!roomId) {
          return {
            success: false,
            message: "房间信息异常",
          };
        }

        const result = await RoomService.addMessage(
          roomId,
          messageType,
          detailData
        );

        if (result.success) {
          // 消息添加成功后刷新消息列表
          await this.loadRoomMessages();

          // 如果房间状态发生变化，更新本地状态
          if (result.data && result.data.roomStatus) {
            console.log("房间状态更新为:", result.data.roomStatus);
          }
        }

        return result;
      } catch (error) {
        console.error("添加房间消息异常:", error);
        return {
          success: false,
          message: "添加消息失败",
        };
      }
    },

    /**
     * 加载房间消息列表
     * @returns {Promise<void>}
     */
    async loadRoomMessages() {
      try {
        const roomId = this.roomInfo?.id;
        if (!roomId) {
          console.warn("房间ID不存在，无法加载消息");
          return;
        }

        const result = await RoomService.getMessages(roomId);

        if (result.success) {
          // 更新消息列表 - 始终替换所有消息
          this.messageData = result.data.messages;

          // 重新计算所有玩家的分数
          this.calculatePlayersScoreFromMessages();

          // 重新计算当前用户的个人流水
          this.calculateCurrentUserTransactions();

          // 只有给分玩法才需要计算所有玩家的流水和记录
          if (!this.isTraditionalView) {
            // 重新计算所有玩家的个人流水
            this.calculateAllPlayersTransactions();

            // 重新计算所有玩家的个人记录
            this.calculateAllPlayersRecords();

            // 更新当前局状态
            this.updateCurrentRoundFromMessages();
          }

          console.log("房间消息加载成功，消息数量:", this.messageData.length);
        } else {
          console.error("加载房间消息失败:", result.message);
          // 失败时初始化为空数组
          this.messageData = [];
        }
      } catch (error) {
        console.error("加载房间消息异常:", error);
        // 异常时初始化为空数组
        this.messageData = [];
      }
    },

    /**
     * 根据消息记录计算所有玩家的分数
     * 遍历所有计分消息，重新计算每个玩家的累计分数
     */
    calculatePlayersScoreFromMessages() {
      try {
        if (!this.players || this.players.length === 0) {
          console.warn("玩家列表为空，跳过分数计算");
          return;
        }

        if (!this.messageData || this.messageData.length === 0) {
          console.log("消息列表为空，重置所有玩家分数为0");
          this.players.forEach((player) => {
            player.score = 0;
          });
          return;
        }

        // 初始化所有玩家分数为0
        const playerScores = {};
        this.players.forEach((player) => {
          playerScores[player.id] = 0;
        });

        let scoreMessageCount = 0;
        let settlementMessageCount = 0;

        // 遍历所有消息，累计分数
        this.messageData.forEach((message) => {
          if (
            message.type === "score" &&
            message.sender &&
            message.target &&
            typeof message.amount === "number"
          ) {
            const senderId = message.sender.id;
            const targetId = message.target.id;
            const receivedAmount = message.amount; // 接收者实际得到的分数（已扣除茶水）

            // 获取原始分数（发送者支付的分数）
            const originalAmount = message.original_amount || message.amount;

            // 发送者减少原始分数，接收者增加实际分数
            if (playerScores.hasOwnProperty(senderId)) {
              playerScores[senderId] = safeMinus(playerScores[senderId], originalAmount);
            }
            if (playerScores.hasOwnProperty(targetId)) {
              playerScores[targetId] = safeAdd(playerScores[targetId], receivedAmount);
            }

            scoreMessageCount++;
          } else if (
            !this.isTraditionalView &&
            message.type === "distribute" &&
            message.sender &&
            typeof message.amount === "number"
          ) {
            // 处理出分消息：发送者分数减少（仅给分玩法）
            const senderId = message.sender.id;
            const amount = message.amount;

            if (playerScores.hasOwnProperty(senderId)) {
              playerScores[senderId] = safeMinus(playerScores[senderId], amount);
            }

            scoreMessageCount++;
          } else if (
            !this.isTraditionalView &&
            message.type === "collect" &&
            message.sender &&
            typeof message.actual_amount === "number"
          ) {
            // 处理收分消息：发送者分数增加（使用实际到账金额）（仅给分玩法）
            const senderId = message.sender.id;
            const actualAmount = message.actual_amount;

            if (playerScores.hasOwnProperty(senderId)) {
              playerScores[senderId] = safeAdd(playerScores[senderId], actualAmount);
            }

            scoreMessageCount++;
          } else if (message.type === "settlement" && message.details) {
            // 处理结算消息，但传统玩法跳过分数调整
            if (this.isTraditionalView) {
              console.log("传统玩法跳过结算消息的分数调整");
            } else {
              // 给分玩法处理结算消息的分数调整
              const adjustments = parseSettlementAdjustments(
                message,
                this.players,
                { teaWaterBalance: this.teaWaterBalance }
              );

              // 应用玩家分数调整
              Object.keys(adjustments).forEach((playerId) => {
                if (playerId === 'tea_water') {
                  // 处理茶水分数调整
                  if (typeof adjustments[playerId] === 'number') {
                    console.log(`应用茶水分数调整: ${this.teaWaterBalance} + ${adjustments[playerId]} = ${this.teaWaterBalance + adjustments[playerId]}`);
                    this.teaWaterBalance = safeAdd(this.teaWaterBalance, adjustments[playerId]);
                  }
                } else if (playerScores.hasOwnProperty(playerId)) {
                  playerScores[playerId] = safeAdd(playerScores[playerId], adjustments[playerId]);
                }
              });
            }

            settlementMessageCount++;
          }
        });

        // 更新玩家列表中的分数
        this.players.forEach((player) => {
          if (playerScores.hasOwnProperty(player.id)) {
            player.score = playerScores[player.id];
          }
        });

        console.log(
          `玩家分数计算完成，处理了${scoreMessageCount}条计分消息和${settlementMessageCount}条结算消息:`,
          playerScores
        );
      } catch (error) {
        console.error("计算玩家分数异常:", error);
        // 异常时重置所有玩家分数为0，避免显示错误数据
        if (this.players) {
          this.players.forEach((player) => {
            player.score = 0;
          });
        }
      }
    },

    /**
     * 根据房间消息计算当前用户的个人流水记录
     * 只计算与当前用户相关的收支流水
     */
    calculateCurrentUserTransactions() {
      try {
        const currentUserId = this.realCurrentUserId;
        if (!currentUserId) {
          console.warn("当前用户ID不存在，跳过个人流水计算");
          return;
        }

        if (!this.messageData || this.messageData.length === 0) {
          // 清空当前用户的流水记录
          if (this.playerTransactions) {
            this.playerTransactions[currentUserId] = [];
          }
          console.log("消息列表为空，清空个人流水记录");
          return;
        }

        const userTransactions = [];

        // 遍历所有计分消息，提取与当前用户相关的流水
        this.messageData.forEach((message) => {
          if (message.type === "score" && message.sender && message.target) {
            const senderId = message.sender.id;
            const targetId = message.target.id;

            if (senderId === currentUserId) {
              // 我给别人计分（支出）
              const originalAmount = message.original_amount || message.amount;
              userTransactions.push({
                id: message.id + "_send",
                timestamp: message.timestamp,
                type: "给分",
                description: `给${message.target.name}计分`,
                amount: -originalAmount,
              });
            } else if (targetId === currentUserId) {
              // 别人给我计分（收入）
              userTransactions.push({
                id: message.id + "_receive",
                timestamp: message.timestamp,
                type: "收分",
                description: `从${message.sender.name}收到计分`,
                amount: message.amount,
              });
            }
          }
        });

        // 按时间排序（最新的在前）
        userTransactions.sort((a, b) => {
          return compareTimestamps(a.timestamp, b.timestamp);
        });

        // 确保 playerTransactions 对象存在
        if (!this.playerTransactions) {
          this.playerTransactions = {};
        }

        // 更新当前用户的流水记录
        this.playerTransactions[currentUserId] = userTransactions;

        console.log(
          `个人流水计算完成，生成${userTransactions.length}条流水记录`
        );
      } catch (error) {
        console.error("计算个人流水异常:", error);
        // 异常时清空当前用户流水，避免显示错误数据
        if (this.playerTransactions && this.realCurrentUserId) {
          this.playerTransactions[this.realCurrentUserId] = [];
        }
      }
    },

    /**
     * 根据房间消息计算所有玩家的个人流水记录
     * 计算每个玩家的收支流水（仅给分玩法需要）
     */
    calculateAllPlayersTransactions() {
      try {
        // 传统玩法不需要计算所有玩家的流水
        if (this.isTraditionalView) {
          console.log("传统玩法跳过所有玩家流水计算");
          return;
        }

        if (!this.players || this.players.length === 0) {
          console.warn("玩家列表为空，跳过所有玩家流水计算");
          return;
        }

        if (!this.messageData || this.messageData.length === 0) {
          console.log("消息列表为空，清空所有玩家流水记录");
          // 清空所有玩家的流水记录
          if (this.playerTransactions) {
            this.players.forEach((player) => {
              this.playerTransactions[player.id] = [];
            });
          }
          return;
        }

        // 确保 playerTransactions 对象存在
        if (!this.playerTransactions) {
          this.playerTransactions = {};
        }

        // 初始化所有玩家的流水记录
        const allTransactions = {};
        this.players.forEach((player) => {
          allTransactions[player.id] = [];
        });

        // 遍历所有消息，提取各类流水记录
        this.messageData.forEach((message) => {
          const messageId =
            message._id || message.id || Date.now() + Math.random();

          if (message.type === "score" && message.sender && message.target) {
            // 计分消息
            const senderId = message.sender.id;
            const targetId = message.target.id;
            const originalAmount = message.original_amount || message.amount;
            const receivedAmount = message.amount;

            // 发送者流水（支出）
            if (allTransactions[senderId]) {
              allTransactions[senderId].push({
                id: messageId + "_send",
                timestamp: message.timestamp,
                type: "给分",
                description: `给${message.target.name}计分`,
                amount: -originalAmount,
              });
            }

            // 接收者流水（收入）
            if (allTransactions[targetId]) {
              allTransactions[targetId].push({
                id: messageId + "_receive",
                timestamp: message.timestamp,
                type: "收分",
                description: `从${message.sender.name}收到计分`,
                amount: receivedAmount,
              });
            }
          } else if (message.type === "distribute" && message.sender) {
            // 出分消息
            const senderId = message.sender.id;
            const amount = message.amount;

            if (allTransactions[senderId]) {
              allTransactions[senderId].push({
                id: messageId + "_distribute",
                timestamp: message.timestamp,
                type: "出分",
                description: "出分到桌面",
                amount: -amount,
              });
            }
          } else if (message.type === "collect" && message.sender) {
            // 收分消息
            const senderId = message.sender.id;
            const actualAmount = message.actual_amount || message.amount;

            if (allTransactions[senderId]) {
              allTransactions[senderId].push({
                id: messageId + "_collect",
                timestamp: message.timestamp,
                type: "收分",
                description: "从桌面收分",
                amount: actualAmount,
              });
            }
          }
        });

        // 对每个玩家的流水按时间排序（最新的在前）
        Object.keys(allTransactions).forEach((playerId) => {
          allTransactions[playerId].sort((a, b) => {
            return compareTimestamps(a.timestamp, b.timestamp);
          });
        });

        // 更新所有玩家的流水记录
        this.playerTransactions = allTransactions;

        console.log("所有玩家流水计算完成");
      } catch (error) {
        console.error("计算所有玩家流水异常:", error);
        // 异常时清空所有流水，避免显示错误数据
        if (this.playerTransactions && this.players) {
          this.players.forEach((player) => {
            this.playerTransactions[player.id] = [];
          });
        }
      }
    },

    /**
     * 根据房间消息计算所有玩家的个人记录
     * 按局数分组计算每个玩家的操作记录（仅给分玩法需要）
     */
    calculateAllPlayersRecords() {
      try {
        // 传统玩法不需要按局数分组的记录
        if (this.isTraditionalView) {
          console.log("传统玩法跳过所有玩家记录计算");
          return;
        }

        if (!this.players || this.players.length === 0) {
          console.warn("玩家列表为空，跳过所有玩家记录计算");
          return;
        }

        if (!this.messageData || this.messageData.length === 0) {
          console.log("消息列表为空，清空所有玩家记录");
          // 清空所有玩家的记录
          if (this.playerRecords) {
            this.players.forEach((player) => {
              this.playerRecords[player.id] = {};
            });
          }
          return;
        }

        // 确保 playerRecords 对象存在
        if (!this.playerRecords) {
          this.playerRecords = {};
        }

        // 初始化所有玩家的记录
        const allRecords = {};
        this.players.forEach((player) => {
          allRecords[player.id] = {};
        });

        // 遍历所有消息，提取各类操作记录
        this.messageData.forEach((message) => {
          const messageId =
            message._id || message.id || Date.now() + Math.random();
          const roundNumber = message.round_number || 1; // 默认为第1局

          if (message.type === "score" && message.sender && message.target) {
            // 计分消息
            const senderId = message.sender.id;
            const targetId = message.target.id;
            const originalAmount = message.original_amount || message.amount;
            const receivedAmount = message.amount;

            // 确保局数记录存在
            if (allRecords[senderId] && !allRecords[senderId][roundNumber]) {
              allRecords[senderId][roundNumber] = [];
            }
            if (allRecords[targetId] && !allRecords[targetId][roundNumber]) {
              allRecords[targetId][roundNumber] = [];
            }

            // 发送者记录
            if (allRecords[senderId] && allRecords[senderId][roundNumber]) {
              allRecords[senderId][roundNumber].push({
                id: messageId + "_send",
                timestamp: formatTimestamp(
                  message.timestamp,
                  "YYYY-MM-DD HH:mm:ss"
                ),
                playerName: message.target.name,
                action: "给其计分",
                amount: -originalAmount,
              });
            }

            // 接收者记录
            if (allRecords[targetId] && allRecords[targetId][roundNumber]) {
              allRecords[targetId][roundNumber].push({
                id: messageId + "_receive",
                timestamp: formatTimestamp(
                  message.timestamp,
                  "YYYY-MM-DD HH:mm:ss"
                ),
                playerName: message.sender.name,
                action: "从其收分",
                amount: receivedAmount,
              });
            }
          } else if (message.type === "distribute" && message.sender) {
            // 出分消息
            const senderId = message.sender.id;
            const amount = message.amount;

            if (allRecords[senderId]) {
              if (!allRecords[senderId][roundNumber]) {
                allRecords[senderId][roundNumber] = [];
              }

              allRecords[senderId][roundNumber].push({
                id: messageId + "_distribute",
                timestamp: formatTimestamp(
                  message.timestamp,
                  "YYYY-MM-DD HH:mm:ss"
                ),
                playerName: "桌面",
                action: "出分到桌面",
                amount: -amount,
              });
            }
          } else if (message.type === "collect" && message.sender) {
            // 收分消息
            const senderId = message.sender.id;
            const actualAmount = message.actual_amount || message.amount;

            if (allRecords[senderId]) {
              if (!allRecords[senderId][roundNumber]) {
                allRecords[senderId][roundNumber] = [];
              }

              allRecords[senderId][roundNumber].push({
                id: messageId + "_collect",
                timestamp: formatTimestamp(
                  message.timestamp,
                  "YYYY-MM-DD HH:mm:ss"
                ),
                playerName: "桌面",
                action: "从桌面收分",
                amount: actualAmount,
              });
            }
          }
        });

        // 对每个玩家每局的记录按时间排序（最新的在前）
        Object.keys(allRecords).forEach((playerId) => {
          Object.keys(allRecords[playerId]).forEach((roundNumber) => {
            allRecords[playerId][roundNumber].sort((a, b) => {
              return (
                new Date(b.timestamp.replace(/-/g, "/")) -
                new Date(a.timestamp.replace(/-/g, "/"))
              );
            });
          });
        });

        // 更新所有玩家的记录
        this.playerRecords = allRecords;

        console.log("所有玩家记录计算完成");
      } catch (error) {
        console.error("计算所有玩家记录异常:", error);
        // 异常时清空所有记录，避免显示错误数据
        if (this.playerRecords && this.players) {
          this.players.forEach((player) => {
            this.playerRecords[player.id] = {};
          });
        }
      }
    },

    // ==================== 滚动控制 ====================

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      this.scrollUpdateCounter++;
      setTimeout(() => {
        this.scrollTop = 100000 + this.scrollUpdateCounter;
      }, 200);
    },

    // ==================== 局状态管理 ====================

    /**
     * 计算当前局状态
     * @returns {Object} 当前局状态信息
     */
    calculateCurrentRoundState() {
      try {
        // 传统玩法不需要局状态管理
        if (this.isTraditionalView) {
          return {
            currentRound: 1,
            tableScore: 0,
            roundStartTime: null,
          };
        }

        if (!this.messageData || this.messageData.length === 0) {
          return {
            currentRound: 1,
            tableScore: 0,
            roundStartTime: null,
          };
        }

        // 查找最新的round_start消息确定当前局数
        let currentRound = 1;
        let roundStartTime = null;
        let tableScore = 0;

        // 从后向前查找最新的round_start消息
        for (let i = this.messageData.length - 1; i >= 0; i--) {
          const message = this.messageData[i];
          if (
            message.type === "round_start" &&
            typeof message.round_number === "number"
          ) {
            currentRound = message.round_number;
            roundStartTime = message.timestamp;
            break;
          }
        }

        // 计算当前局的桌面分数
        this.messageData.forEach((message) => {
          if (message.round_number === currentRound) {
            if (
              message.type === "distribute" &&
              typeof message.amount === "number"
            ) {
              tableScore = safeAdd(tableScore, message.amount);
            } else if (
              message.type === "collect" &&
              typeof message.original_amount === "number"
            ) {
              tableScore = safeMinus(tableScore, message.original_amount);
            }
          }
        });

        return {
          currentRound,
          tableScore,
          roundStartTime,
        };
      } catch (error) {
        console.error("计算当前局状态失败:", error);
        return {
          currentRound: 1,
          tableScore: 0,
          roundStartTime: null,
        };
      }
    },

    /**
     * 计算玩家局内统计
     * @param {string} playerId - 玩家ID
     * @param {number} roundNumber - 局数
     * @returns {Object} 玩家统计信息
     */
    calculatePlayerRoundStats(playerId, roundNumber) {
      try {
        // 传统玩法不需要局内统计
        if (this.isTraditionalView) {
          return {
            distributeCount: 0,
            collectCount: 0,
          };
        }

        if (!this.messageData || !playerId || typeof roundNumber !== "number") {
          return {
            distributeCount: 0,
            collectCount: 0,
          };
        }

        let distributeCount = 0;
        let collectCount = 0;

        this.messageData.forEach((message) => {
          if (
            message.round_number === roundNumber &&
            message.sender &&
            message.sender.id === playerId
          ) {
            if (message.type === "distribute") {
              distributeCount++;
            } else if (message.type === "collect") {
              collectCount++;
            }
          }
        });

        return {
          distributeCount,
          collectCount,
        };
      } catch (error) {
        console.error("计算玩家局内统计失败:", error);
        return {
          distributeCount: 0,
          collectCount: 0,
        };
      }
    },

    /**
     * 从消息更新当前局数
     * @returns {void}
     */
    updateCurrentRoundFromMessages() {
      try {
        // 传统玩法不需要局数管理
        if (this.isTraditionalView) {
          console.log("传统玩法跳过局数更新");
          return;
        }

        const roundState = this.calculateCurrentRoundState();

        // 更新当前局数
        if (this.currentRound !== roundState.currentRound) {
          console.log(
            `局数更新: ${this.currentRound} -> ${roundState.currentRound}`
          );
          this.currentRound = roundState.currentRound;
        }

        // 更新桌面分数
        if (this.tableScore !== roundState.tableScore) {
          console.log(
            `桌面分数更新: ${this.tableScore} -> ${roundState.tableScore}`
          );
          this.tableScore = roundState.tableScore;
        }
      } catch (error) {
        console.error("从消息更新当前局数失败:", error);
      }
    },
  },
};
