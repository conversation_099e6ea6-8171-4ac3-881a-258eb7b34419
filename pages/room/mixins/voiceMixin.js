// 语音播报 Mixin
// 负责处理微信同声传译插件的语音合成功能

export default {
  data() {
    return {
      // 语音播报相关状态
      voicePlugin: null,
      isSpeaking: false,
      speechQueue: [],
      lastSpokenMessageId: null,
      speechConfig: {
        lang: "zh_CN",
        tts: true,
      },

      // 错误处理相关
      voiceErrorCount: 0,
      maxVoiceErrors: 3,
      voiceErrorTimeout: 5 * 60 * 1000, // 5分钟后重试
      lastVoiceErrorTime: 0,
      isVoiceDisabledByError: false,
    };
  },

  async mounted() {
    // 初始化微信同声传译插件
    await this.initVoicePlugin();
  },

  beforeDestroy() {
    // 清理语音资源
    this.cleanupVoice();
  },

  methods: {
    // ==================== 语音插件初始化 ====================

    /**
     * 初始化微信同声传译插件
     */
    async initVoicePlugin() {
      try {
        // 获取微信同声传译插件
        this.voicePlugin = requirePlugin("WechatSI");
        if (!this.voicePlugin) {
          console.warn("微信同声传译插件加载失败，语音播报功能将被禁用");
          return;
        }

        console.log("微信同声传译插件初始化成功");
      } catch (error) {
        console.error("初始化语音插件失败:", error);
        this.voicePlugin = null;
      }
    },

    // ==================== 核心语音播报方法 ====================

    /**
     * 播报文本消息
     * @param {string} text 要播报的文本
     * @returns {Promise<boolean>} 播报是否成功
     */
    async speakMessage(text) {
      try {
        // 检查前置条件
        if (!this.canSpeak(text)) {
          return false;
        }

        // 添加到播报队列
        return await this.addToSpeechQueue(text);
      } catch (error) {
        console.error("语音播报失败:", error);
        return false;
      }
    },

    /**
     * 批量播报新消息
     * @param {Array} messages 新消息数组
     */
    async speakNewMessages(messages) {
      if (!Array.isArray(messages) || messages.length === 0) {
        return;
      }

      // 过滤需要播报的消息
      const messagesToSpeak = messages.filter((message) =>
        this.shouldSpeakMessage(message)
      );

      // 逐条播报
      for (const message of messagesToSpeak) {
        const speechText = this.convertMessageToSpeechText(message);
        if (speechText) {
          await this.speakMessage(speechText);
          // 记录最后播报的消息，避免重复播报
          this.lastSpokenMessageId = message.id || message._id;
        }
      }
    },

    // ==================== 消息转换逻辑 ====================

    /**
     * 将消息转换为语音文本
     * @param {Object} message 消息对象
     * @returns {string} 语音文本
     */
    convertMessageToSpeechText(message) {
      try {
        switch (message.type) {
          case "score":
            return this.convertScoreMessage(message);
          case "distribute":
            return this.convertDistributeMessage(message);
          case "collect":
            return this.convertCollectMessage(message);
          case "settlement":
            return this.convertSettlementMessage(message);
          case "system":
            return this.convertSystemMessage(message);
          default:
            return null;
        }
      } catch (error) {
        console.error("消息转换失败:", error);
        return null;
      }
    },

    /**
     * 转换计分消息
     * @param {Object} message 计分消息
     * @returns {string} 语音文本
     */
    convertScoreMessage(message) {
      const senderName = message.sender?.name || "某人";
      const targetName = message.target?.name || "对方";
      const amount = Math.abs(message._displayAmount || message.amount || 0);

      return `${senderName}给${targetName}记分${amount}`;
    },

    /**
     * 转换出分消息
     * @param {Object} message 出分消息
     * @returns {string} 语音文本
     */
    convertDistributeMessage(message) {
      const senderName = message.sender?.name || "某人";
      const amount = Math.abs(message.amount || 0);

      return `${senderName}出分到桌面${amount}`;
    },

    /**
     * 转换收分消息
     * @param {Object} message 收分消息
     * @returns {string} 语音文本
     */
    convertCollectMessage(message) {
      const senderName = message.sender?.name || "某人";
      const amount = Math.abs(message.actual_amount || message.amount || 0);

      return `${senderName}从桌面收分${amount}`;
    },

    /**
     * 转换结算消息
     * @param {Object} message 结算消息
     * @returns {string} 语音文本
     */
    convertSettlementMessage(message) {
      const senderName = message.sender?.name || "某人";
      
      // 根据结算类型播报不同内容
      if (message.details && message.details.settlement_type) {
        switch (message.details.settlement_type) {
          case "round_settlement":
            return `${senderName}发起了局结算`;
          case "final_settlement":
            return `${senderName}发起了最终结算`;
          default:
            return `${senderName}发起了结算`;
        }
      }
      
      return `${senderName}发起了结算`;
    },

    /**
     * 转换系统消息
     * @param {Object} message 系统消息
     * @returns {string} 语音文本
     */
    convertSystemMessage(message) {
      // 只播报茶水设置相关的系统消息
      if (message.actionType === "tea_settings") {
        const senderName = message.sender?.name || "某人";
        return `${senderName}修改了茶水设置`;
      }
      return null;
    },

    // ==================== 播报控制逻辑 ====================

    /**
     * 检查是否可以播报
     * @param {string} text 要播报的文本
     * @returns {boolean} 是否可以播报
     */
    canSpeak(text) {
      // 检查声音是否开启
      if (!this.soundEnabled) {
        return false;
      }

      // 检查插件是否可用
      if (!this.voicePlugin) {
        return false;
      }

      // 检查文本是否有效
      if (!text || typeof text !== "string" || text.trim().length === 0) {
        return false;
      }

      // 检查是否因错误被临时禁用
      if (this.isVoiceDisabledByError) {
        const now = Date.now();
        if (now - this.lastVoiceErrorTime < this.voiceErrorTimeout) {
          return false;
        } else {
          // 错误超时，重置错误状态
          this.resetVoiceErrorState();
        }
      }

      return true;
    },

    /**
     * 检查消息是否需要播报
     * @param {Object} message 消息对象
     * @returns {boolean} 是否需要播报
     */
    shouldSpeakMessage(message) {
      // 排除不需要播报的消息类型（只排除局开始和局结束，结算消息允许播报）
      const excludeTypes = ["round_start", "round_end"];
      if (excludeTypes.includes(message.type)) {
        return false;
      }

      // 避免重复播报同一消息
      const messageId = message.id || message._id;
      if (messageId && messageId === this.lastSpokenMessageId) {
        return false;
      }

      return true;
    },

    // ==================== 播报队列管理 ====================

    /**
     * 添加到播报队列
     * @param {string} text 要播报的文本
     * @returns {Promise<boolean>} 播报是否成功
     */
    async addToSpeechQueue(text) {
      return new Promise((resolve) => {
        this.speechQueue.push({
          text,
          resolve,
          timestamp: Date.now(),
        });

        // 如果当前没有在播报，立即开始播报
        if (!this.isSpeaking) {
          this.processSpeechQueue();
        }
      });
    },

    /**
     * 处理播报队列
     */
    async processSpeechQueue() {
      if (this.isSpeaking || this.speechQueue.length === 0) {
        return;
      }

      this.isSpeaking = true;

      while (this.speechQueue.length > 0) {
        const speechItem = this.speechQueue.shift();

        try {
          await this.performTextToSpeech(speechItem.text);
          speechItem.resolve(true);
        } catch (error) {
          console.error("播报失败:", error);
          speechItem.resolve(false);
        }

        // 播报间隔，避免语音重叠
        await this.delay(3000);
      }

      this.isSpeaking = false;
    },

    /**
     * 执行文本转语音
     * @param {string} text 要播报的文本
     * @returns {Promise<void>}
     */
    async performTextToSpeech(text) {
      return new Promise((resolve, reject) => {
        if (!this.voicePlugin) {
          reject(new Error("语音插件不可用"));
          return;
        }

        // 添加超时处理
        const timeout = setTimeout(() => {
          console.warn("语音播报超时:", text);
          this.handleVoiceError(new Error("语音播报超时"));
          reject(new Error("语音播报超时"));
        }, 10000); // 10秒超时
        console.log("--------------");
        this.voicePlugin.textToSpeech({
          ...this.speechConfig,
          content: text,
          success: (res) => {
            clearTimeout(timeout);
            const innerAudioContext = uni.createInnerAudioContext();
            innerAudioContext.src = res.filename;
            innerAudioContext.play();
            console.log("语音播报成功:", text);
            // 成功时重置错误计数
            this.resetVoiceErrorCount();
            resolve(res);
          },
          fail: (err) => {
            clearTimeout(timeout);
            console.error("语音播报失败:", err);
            this.handleVoiceError(err);
            reject(err);
          },
        });
      });
    },

    // ==================== 工具方法 ====================

    /**
     * 延迟方法
     * @param {number} ms 延迟毫秒数
     * @returns {Promise<void>}
     */
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },

    /**
     * 清理语音资源
     */
    cleanupVoice() {
      // 清空播报队列
      this.speechQueue = [];
      this.isSpeaking = false;
      this.lastSpokenMessageId = null;

      // 停止当前播报
      if (this.voicePlugin) {
        try {
          // 注意：微信同声传译插件可能没有停止方法，这里做静默处理
          // this.voicePlugin.stop && this.voicePlugin.stop();
        } catch (error) {
          console.warn("停止语音播报时出错:", error);
        }
      }
    },

    // ==================== 错误处理方法 ====================

    /**
     * 处理语音错误
     * @param {Error} error 错误对象
     */
    handleVoiceError(error) {
      this.voiceErrorCount++;
      this.lastVoiceErrorTime = Date.now();

      console.warn(
        `语音播报错误 ${this.voiceErrorCount}/${this.maxVoiceErrors}:`,
        error
      );

      // 如果错误次数超过阈值，临时禁用语音播报
      if (this.voiceErrorCount >= this.maxVoiceErrors) {
        this.isVoiceDisabledByError = true;
        console.warn(
          `语音播报因连续错误被临时禁用，将在${
            this.voiceErrorTimeout / 1000 / 60
          }分钟后自动恢复`
        );

        // 清理当前播报队列
        this.speechQueue = [];
        this.isSpeaking = false;
      }
    },

    /**
     * 重置语音错误状态
     */
    resetVoiceErrorState() {
      this.voiceErrorCount = 0;
      this.isVoiceDisabledByError = false;
      this.lastVoiceErrorTime = 0;
      console.log("语音播报错误状态已重置");
    },

    /**
     * 重置语音错误计数（播报成功时调用）
     */
    resetVoiceErrorCount() {
      if (this.voiceErrorCount > 0) {
        this.voiceErrorCount = 0;
        console.log("语音播报错误计数已重置");
      }
    },
  },
};
