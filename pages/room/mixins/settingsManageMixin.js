// 设置管理 Mixin
// 负责处理声音、屏幕常亮等应用设置

import { showToast } from "@/utils/commonMethods.js";

// 存储配置
const SETTINGS_CONFIG = {
  STORAGE_KEY: "suixin_scorer_app_settings",
  DEFAULT_SETTINGS: {
    soundEnabled: false,
    screenWakeLockEnabled: false,
  }
};

// 设置存储工具对象
const settingsStorage = {
  /**
   * 获取所有设置
   * @returns {Object} 设置对象
   */
  getSettings() {
    try {
      const settings = uni.getStorageSync(SETTINGS_CONFIG.STORAGE_KEY);
      if (settings && typeof settings === 'object') {
        // 合并默认设置，确保所有必需的字段都存在
        return { ...SETTINGS_CONFIG.DEFAULT_SETTINGS, ...settings };
      }
      return { ...SETTINGS_CONFIG.DEFAULT_SETTINGS };
    } catch (error) {
      console.warn('获取应用设置失败，使用默认设置:', error);
      return { ...SETTINGS_CONFIG.DEFAULT_SETTINGS };
    }
  },

  /**
   * 保存所有设置
   * @param {Object} settings 设置对象
   */
  saveSettings(settings) {
    try {
      uni.setStorageSync(SETTINGS_CONFIG.STORAGE_KEY, settings);
    } catch (error) {
      console.warn('保存应用设置失败:', error);
    }
  },

  /**
   * 获取单个设置
   * @param {string} key 设置键名
   * @param {*} defaultValue 默认值
   * @returns {*} 设置值
   */
  getSetting(key, defaultValue) {
    const settings = this.getSettings();
    return settings.hasOwnProperty(key) ? settings[key] : defaultValue;
  },

  /**
   * 保存单个设置
   * @param {string} key 设置键名
   * @param {*} value 设置值
   */
  setSetting(key, value) {
    const settings = this.getSettings();
    settings[key] = value;
    this.saveSettings(settings);
  }
};

export default {
  data() {
    return {
      // 设置相关
      soundEnabled: false,
      screenWakeLockEnabled: false,
      wakeLock: null,
    };
  },

  mounted() {
    // 从本地存储加载设置
    this.loadSettingsFromStorage();
    // 初始化屏幕常亮
    this.initializeWakeLock();
  },

  beforeDestroy() {
    // 释放屏幕常亮
    this.releaseWakeLock();
  },

  methods: {
    // ==================== 本地存储相关方法 ====================
    
    /**
     * 从本地存储加载设置
     */
    loadSettingsFromStorage() {
      try {
        const savedSettings = settingsStorage.getSettings();
        
        // 应用保存的设置到当前实例
        this.soundEnabled = savedSettings.soundEnabled;
        this.screenWakeLockEnabled = savedSettings.screenWakeLockEnabled;
        
        console.log('应用设置已从本地存储加载:', savedSettings);
      } catch (error) {
        console.warn('加载应用设置失败，使用默认值:', error);
        // 确保使用默认值
        this.soundEnabled = SETTINGS_CONFIG.DEFAULT_SETTINGS.soundEnabled;
        this.screenWakeLockEnabled = SETTINGS_CONFIG.DEFAULT_SETTINGS.screenWakeLockEnabled;
      }
    },

    // ==================== 设置相关方法 ====================
    
    /**
     * 初始化屏幕常亮
     */
    initializeWakeLock() {
      if (this.screenWakeLockEnabled) {
        this.requestWakeLock();
      }
    },

    /**
     * 切换声音开关
     */
    toggleSound() {
      this.soundEnabled = !this.soundEnabled;
      
      // 自动保存到本地存储
      settingsStorage.setSetting('soundEnabled', this.soundEnabled);
      
      if (this.soundEnabled) {
        // 开启语音播报时播放测试音
        this.playTestSound();
        showToast("已开启语音播报", "none");
      } else {
        // 关闭声音时清理语音播报队列
        this.cleanupVoiceOnSoundDisabled();
        showToast("已关闭语音播报", "none");
      }
      
      this.vibrateOnSuccess();
    },

    /**
     * 播放测试音（声音开启时）
     */
    playTestSound() {
      // 检查是否有语音播报功能可用
      if (this.speakMessage && typeof this.speakMessage === 'function') {
        // 延迟一点播放测试音，让提示先显示
        setTimeout(() => {
          this.speakMessage('语音播报已开启');
        }, 500);
      }
    },

    /**
     * 声音关闭时清理语音相关资源
     */
    cleanupVoiceOnSoundDisabled() {
      // 检查是否有语音清理功能可用
      if (this.cleanupVoice && typeof this.cleanupVoice === 'function') {
        this.cleanupVoice();
      }
    },

    /**
     * 切换屏幕常亮开关
     */
    toggleScreenWakeLock() {
      this.screenWakeLockEnabled = !this.screenWakeLockEnabled;

      // 自动保存到本地存储
      settingsStorage.setSetting('screenWakeLockEnabled', this.screenWakeLockEnabled);

      if (this.screenWakeLockEnabled) {
        this.requestWakeLock();
      } else {
        this.releaseWakeLock();
      }

      showToast(
        this.screenWakeLockEnabled ? "屏幕将保持常亮" : "已关闭屏幕常亮",
        "none"
      );
      this.vibrateOnSuccess();
    },

    /**
     * 请求屏幕常亮
     */
    async requestWakeLock() {
      try {
        if (typeof navigator !== "undefined" && navigator.wakeLock) {
          this.wakeLock = await navigator.wakeLock.request("screen");
        } else {
          uni.setKeepScreenOn({ keepScreenOn: true });
        }
      } catch (err) {
        console.error(`获取屏幕常亮失败: ${err.message}`);
      }
    },

    /**
     * 释放屏幕常亮
     */
    releaseWakeLock() {
      try {
        if (this.wakeLock) {
          this.wakeLock.release();
          this.wakeLock = null;
        } else {
          uni.setKeepScreenOn({ keepScreenOn: false });
        }
      } catch (err) {
        console.error(`释放屏幕常亮失败: ${err.message}`);
      }
    },

    /**
     * 操作成功后短振动
     */
    vibrateOnSuccess() {
      uni.vibrateShort({
        fail: (err) => {
          console.error("vibrate failed", err);
        },
      });
    },
  }
}; 