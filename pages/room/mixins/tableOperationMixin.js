// 桌面操作 Mixin
// 负责处理桌面分数的出分和收分操作

import { showToast } from "@/utils/commonMethods.js";
import { safeAdd, safeMinus } from "@/utils/mathUtils.js";

export default {
  methods: {
    // ==================== 桌面分数操作 ====================
    
    /**
     * 处理出分确认
     * @param {number} amount 出分金额
     */
    async handleDistributeConfirm(amount) {
      const currentPlayer = this.getCurrentUser();
      if (!currentPlayer) {
        showToast("获取玩家信息失败", "none");
        return;
      }

      try {
        // 优先尝试WebSocket方式
        if (this.isWebSocketEnabled && this.wsDistributeScore) {
          console.log('尝试使用WebSocket进行出分');
          
          const wsResult = await this.wsDistributeScore(amount);
          
          if (wsResult.success) {
            // WebSocket成功，显示成功提示并关闭弹窗
            showToast("出分成功");
            this.closeDistributeModal();
            console.log('WebSocket出分成功');
            return;
          } else {
            console.warn('WebSocket出分失败，降级到云对象模式:', wsResult.message);
          }
        }
        
        // WebSocket失败或不可用，降级到原有的云对象方式
        console.log('使用云对象进行出分');
        await this.handleDistributeConfirmByCloudFunction(amount);
        
      } catch (error) {
        console.error('出分操作失败:', error);
        showToast("出分失败，请重试", "none");
      }
    },

    /**
     * 通过云对象处理出分确认（原有逻辑）
     * @param {number} amount 出分金额
     */
    async handleDistributeConfirmByCloudFunction(amount) {
      try {
        // 创建出分消息
        const result = await this.createDistributeMessage(amount);
        if (result.success) {
          showToast("出分成功");
          this.closeDistributeModal();
        } else {
          showToast(result.message || "出分失败，请重试", "none");
        }
      } catch (error) {
        console.error('云对象出分操作失败:', error);
        showToast("出分失败，请重试", "none");
      }
    },

    /**
     * 创建出分消息
     * @param {number} amount 出分金额
     * @returns {Promise<Object>} 操作结果
     */
    async createDistributeMessage(amount) {
      try {
        const tableScoreBefore = this.tableScore;
        const tableScoreAfter = safeAdd(tableScoreBefore, amount);
        
        // 创建distribute消息
        const messageResult = await this.addRoomMessage("distribute", {
          amount: amount,
          round_number: this.currentRound,
          table_score_before: tableScoreBefore,
          table_score_after: tableScoreAfter
        });

        if (messageResult.success) {
          // 添加流水记录
          if (this.addTransactionRecord) {
            this.addTransactionRecord(
              this.realCurrentUserId,
              "出分",
              `第${this.currentRound}局出分到桌面`,
              -amount
            );
          }
        }

        return messageResult;
      } catch (error) {
        console.error('创建出分消息失败:', error);
        return {
          success: false,
          message: '出分操作失败'
        };
      }
    },

    /**
     * 处理收分确认
     * @param {number} amount 收分金额
     */
    async handleCollectConfirm(amount) {
      const currentPlayer = this.getCurrentUser();
      if (!currentPlayer) {
        showToast("获取玩家信息失败", "none");
        return;
      }

      try {
        // 优先尝试WebSocket方式
        if (this.isWebSocketEnabled && this.wsCollectScore) {
          console.log('尝试使用WebSocket进行收分');
          
          const wsResult = await this.wsCollectScore(amount);
          
          if (wsResult.success) {
            // WebSocket成功，显示成功提示并关闭弹窗
            showToast("收分成功");
            this.closeCollectModal();
            console.log('WebSocket收分成功');
            return;
          } else {
            console.warn('WebSocket收分失败，降级到云对象模式:', wsResult.message);
          }
        }
        
        // WebSocket失败或不可用，降级到原有的云对象方式
        console.log('使用云对象进行收分');
        await this.handleCollectConfirmByCloudFunction(amount);
        
      } catch (error) {
        console.error('收分操作失败:', error);
        showToast("收分失败，请重试", "none");
      }
    },

    /**
     * 通过云对象处理收分确认（原有逻辑）
     * @param {number} amount 收分金额
     */
    async handleCollectConfirmByCloudFunction(amount) {
      try {
        // 创建收分消息
        const result = await this.createCollectMessage(amount);
        if (result.success) {
          // 检查是否需要自动下一局
          await this.checkAndTriggerNextRound();
          showToast("收分成功");
          this.closeCollectModal();
        } else {
          showToast(result.message || "收分失败，请重试", "none");
        }
      } catch (error) {
        console.error('云对象收分操作失败:', error);
        showToast("收分失败，请重试", "none");
      }
    },

    /**
     * 创建收分消息
     * @param {number} amount 收分金额
     * @returns {Promise<Object>} 操作结果
     */
    async createCollectMessage(amount) {
      try {
        const tableScoreBefore = this.tableScore;
        
        // 计算茶水抽取
        const { teaAmount, actualAmount } = this.calculateTeaWaterDeduction 
          ? this.calculateTeaWaterDeduction(amount) 
          : { teaAmount: 0, actualAmount: amount };
        
        const tableScoreAfter = safeMinus(tableScoreBefore, amount);
        
        // 创建collect消息
        const messageResult = await this.addRoomMessage("collect", {
          original_amount: amount,
          actual_amount: actualAmount,
          tea_amount: teaAmount,
          round_number: this.currentRound,
          table_score_before: tableScoreBefore,
          table_score_after: tableScoreAfter
        });

        if (messageResult.success) {
          // 更新茶水余额
          if (teaAmount > 0 && this.handleTeaAmountCollected) {
            this.handleTeaAmountCollected(teaAmount);
          }
          
          // 添加流水记录
          if (this.addTransactionRecord) {
            this.addTransactionRecord(
              this.realCurrentUserId,
              "收分",
              `第${this.currentRound}局从桌面收分`,
              actualAmount
            );
          }
        }

        return messageResult;
      } catch (error) {
        console.error('创建收分消息失败:', error);
        return {
          success: false,
          message: '收分操作失败'
        };
      }
    },

    /**
     * 检查并触发下一局
     * @returns {Promise<void>}
     */
    async checkAndTriggerNextRound() {
      try {
        // 检查桌面分数是否为0
        if (this.tableScore === 0) {
          console.log('桌面分数为0，自动进入下一局');
          
          // 发送局结束消息
          const endResult = await this.addRoomMessage("round_end", {
            round_number: this.currentRound,
            message_text: `第${this.currentRound}局结束`,
            trigger_reason: "table_score_zero",
            final_table_score: 0
          });

          if (endResult.success) {
            // 增加局数
            const nextRound = this.currentRound + 1;
            
            // 发送新局开始消息
            const startResult = await this.addRoomMessage("round_start", {
              round_number: nextRound,
              message_text: `第${nextRound}局开始`,
              trigger_reason: "auto"
            });

            if (startResult.success) {
              console.log(`自动切换到第${nextRound}局成功`);
            } else {
              console.error('发送新局开始消息失败:', startResult.message);
            }
          } else {
            console.error('发送局结束消息失败:', endResult.message);
          }
        }
      } catch (error) {
        console.error('检查并触发下一局失败:', error);
      }
    },
  }
}; 