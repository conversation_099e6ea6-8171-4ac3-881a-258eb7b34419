/**
 * 网络状态检测Mixin
 * 基于uni-app官方网络状态API实现真实的网络状态检测
 * 
 * 使用方法：
 * import networkStatusMixin from './mixins/networkStatusMixin.js'
 * 
 * export default {
 *   mixins: [networkStatusMixin],
 *   // ...其他配置
 * }
 */

import { formatToLocaleTimeString, now } from "@/utils/dateUtils.js";

export default {
  data() {
    return {
      // 网络状态: 'good', 'poor', 'disconnected'
      networkStatus: 'good',
      
      // 原始网络类型 (从uni.getNetworkType获取)
      rawNetworkType: 'wifi',
      
      // 网络连接状态
      isNetworkConnected: true,
      
      // 网络状态监听器引用
      networkStatusCallback: null,
    };
  },

  computed: {
    // 网络状态文本
    networkStatusText() {
      switch (this.networkStatus) {
        case 'good':
          return '网络良好';
        case 'poor':
          return '网络一般';
        case 'disconnected':
          return '网络断开';
        default:
          return '网络良好';
      }
    },

    // 网络状态样式类
    networkStatusClass() {
      return this.networkStatus;
    },

    // 网络类型详细信息
    networkTypeText() {
      switch (this.rawNetworkType) {
        case 'wifi':
          return 'WiFi';
        case '5g':
          return '5G网络';
        case '4g':
          return '4G网络';
        case '3g':
          return '3G网络';
        case '2g':
          return '2G网络';
        case 'ethernet':
          return '有线网络';
        case 'unknown':
          return '未知网络';
        case 'none':
          return '无网络';
        default:
          return '网络';
      }
    },
  },

  mounted() {
    this.initNetworkStatus();
  },

  beforeDestroy() {
    this.destroyNetworkStatus();
  },

  methods: {
    /**
     * 初始化网络状态检测
     */
    async initNetworkStatus() {
      try {
        // 获取初始网络状态
        await this.getCurrentNetworkStatus();
        
        // 开始监听网络状态变化
        this.startNetworkStatusListener();
        
        console.log('[NetworkStatus] 网络状态初始化完成:', {
          status: this.networkStatus,
          type: this.rawNetworkType,
          connected: this.isNetworkConnected
        });
      } catch (error) {
        console.error('[NetworkStatus] 初始化网络状态失败:', error);
        // 初始化失败时设置默认状态
        this.updateNetworkStatusInternal('good', 'wifi', true);
      }
    },

    /**
     * 获取当前网络状态
     */
    getCurrentNetworkStatus() {
      return new Promise((resolve, reject) => {
        uni.getNetworkType({
          success: (res) => {
            const networkType = res.networkType;
            const isConnected = networkType !== 'none';
            const status = this.mapNetworkTypeToStatus(networkType);
            
            this.updateNetworkStatusInternal(status, networkType, isConnected);
            
            console.log('[NetworkStatus] 获取网络状态成功:', {
              networkType,
              status,
              isConnected
            });
            
            resolve(res);
          },
          fail: (err) => {
            console.error('[NetworkStatus] 获取网络状态失败:', err);
            reject(err);
          }
        });
      });
    },

    /**
     * 开始监听网络状态变化
     */
    startNetworkStatusListener() {
      // 创建网络状态变化回调函数
      this.networkStatusCallback = (res) => {
        const { networkType, isConnected } = res;
        const status = this.mapNetworkTypeToStatus(networkType);
        
        this.updateNetworkStatusInternal(status, networkType, isConnected);
        
        console.log('[NetworkStatus] 网络状态变化:', {
          networkType,
          isConnected,
          status,
          timestamp: formatToLocaleTimeString(now())
        });

        // 触发自定义事件，让页面可以响应网络状态变化
        this.onNetworkStatusChanged && this.onNetworkStatusChanged({
          status,
          networkType,
          isConnected,
          statusText: this.networkStatusText,
          typeText: this.networkTypeText
        });
      };

      // 开始监听
      uni.onNetworkStatusChange(this.networkStatusCallback);
    },

    /**
     * 停止网络状态监听
     */
    stopNetworkStatusListener() {
      if (this.networkStatusCallback) {
        uni.offNetworkStatusChange(this.networkStatusCallback);
        this.networkStatusCallback = null;
        console.log('[NetworkStatus] 已停止网络状态监听');
      }
    },

    /**
     * 销毁网络状态检测
     */
    destroyNetworkStatus() {
      this.stopNetworkStatusListener();
    },

    /**
     * 将uni-app的网络类型映射为页面使用的状态
     * @param {string} networkType uni.getNetworkType返回的网络类型
     * @returns {string} 'good' | 'poor' | 'disconnected'
     */
    mapNetworkTypeToStatus(networkType) {
      switch (networkType) {
        case 'wifi':
        case '5g':
        case '4g':
        case 'ethernet':
          return 'good';
        
        case '3g':
        case '2g':
        case 'unknown':
          return 'poor';
        
        case 'none':
          return 'disconnected';
        
        default:
          return 'good';
      }
    },

    /**
     * 内部更新网络状态方法
     * @param {string} status 网络状态
     * @param {string} networkType 网络类型
     * @param {boolean} isConnected 是否连接
     */
    updateNetworkStatusInternal(status, networkType, isConnected) {
      this.networkStatus = status;
      this.rawNetworkType = networkType;
      this.isNetworkConnected = isConnected;
    },

    /**
     * 手动更新网络状态（供外部调用）
     * @param {string} status 网络状态 'good' | 'poor' | 'disconnected'
     */
    updateNetworkStatus(status) {
      if (['good', 'poor', 'disconnected'].includes(status)) {
        this.networkStatus = status;
        console.log('[NetworkStatus] 手动更新网络状态:', status);
      } else {
        console.warn('[NetworkStatus] 无效的网络状态:', status);
      }
    },

    /**
     * 刷新网络状态
     */
    async refreshNetworkStatus() {
      try {
        await this.getCurrentNetworkStatus();
        return {
          success: true,
          status: this.networkStatus,
          networkType: this.rawNetworkType,
          isConnected: this.isNetworkConnected
        };
      } catch (error) {
        console.error('[NetworkStatus] 刷新网络状态失败:', error);
        return {
          success: false,
          error: error.message || '刷新失败'
        };
      }
    },

    /**
     * 获取网络状态信息对象
     * @returns {object} 包含所有网络状态信息的对象
     */
    getNetworkStatusInfo() {
      return {
        status: this.networkStatus,
        statusText: this.networkStatusText,
        networkType: this.rawNetworkType,
        networkTypeText: this.networkTypeText,
        isConnected: this.isNetworkConnected,
        className: this.networkStatusClass
      };
    },
  }
}; 