<template>
  <div class="room-container">
    <!-- 左侧工具栏 -->
    <div class="sidebar">
      <div class="top-players-area">
        <!-- 邀请好友 -->
        <div
          class="sidebar-item invite-friends"
          @click="inviteFriends"
          v-if="canInviteMore"
        >
          <div class="avatar function-avatar">
            <span class="iconfont function-icon">&#xe87a;</span>
          </div>
          <div class="function-name">邀请好友</div>
        </div>

        <!-- 玩家列表区域 -->
        <scroll-view
          class="players-list"
          :enable-flex="true"
          scroll-y="true"
          :show-scrollbar="false"
          :scroll-with-animation="true"
          :refresher-enabled="false"
        >
          <!-- 玩家列表 -->
          <div class="player-item" v-for="player in activePlayers" :key="player.id">
            <div
              class="player-avatar-wrapper"
              @click="handleAvatarClick(player)"
            >
              <div class="avatar" :class="getPlayerAvatarClass(player)">
                <avatar-display
                  style="width: 44px; height: 44px"
                  :avatarFileId="player.avatarFileId"
                  size="44px"
                />
              </div>
              <div
                class="score-badge"
                :class="getScoreBadgeClass(player.score)"
                :style="{ fontSize: getScoreFontSize(player.score) }"
                v-if="player.score !== 0"
              >
                {{ formatScoreDisplay(player.score) }}
              </div>
            </div>
            <div class="player-name">{{ player.name }}</div>
          </div>
        </scroll-view>

        <!-- 茶水按钮 -->
        <div class="sidebar-item tea-button" @click="showTeaSettings">
          <div class="tea-avatar-wrapper">
            <div class="avatar function-avatar tea-avatar">
              <span class="iconfont function-icon">&#xe878;</span>
            </div>
            <div
              class="score-badge tea-score-badge"
              :class="getTeaScoreBadgeClass"
              :style="{ fontSize: getTeaScoreFontSize }"
              v-if="teaWaterBalance !== 0"
            >
              {{ formatScoreDisplay(teaWaterBalance) }}
            </div>
          </div>
          <div class="function-name">茶水</div>
        </div>
      </div>

      <!-- 底部功能按钮 -->
      <div class="bottom-buttons">
        <div
          v-for="button in bottomButtons"
          :key="button.name"
          class="function-button"
          @click="button.action"
        >
          <span class="iconfont bottom-icon">{{ button.icon }}</span>
          <div class="function-name">{{ button.name }}</div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content">
      <!-- 顶部提示条 -->
      <div class="top-hint">
        <div class="hint-scroll-container">
          <div class="hint-text-wrapper">
            <text
              v-for="(hint, index) in currentHints"
              :key="index"
              class="hint-text"
              :class="{ active: index === currentHintIndex }"
            >
              {{ hint }}
            </text>
          </div>
        </div>
        <div class="network-status" :class="networkStatusClass">
          <text>{{ networkStatusText }}</text>
          <div class="status-dot" :class="networkStatusClass"></div>
        </div>
      </div>

      <!-- 传统记分区域 -->
      <div v-if="isTraditionalView" class="messages-area">
        <scroll-view
          scroll-y="true"
          :enable-passive="true"
          :scroll-top="scrollTop"
          :scroll-with-animation="true"
          class="messages-scroll-view"
          :show-scrollbar="false"
          :id="messagesScrollViewId"
        >
          <traditional-list
            v-if="isTraditionalView && realCurrentUserId"
            :list="messageData"
            :currentUserId="realCurrentUserId"
          ></traditional-list>
        </scroll-view>
      </div>

      <!-- 给分桌面区域 -->
      <div v-else class="table-area">
        <!-- 牌桌部分 -->
        <div class="table-container" @click="viewRecords">
          <div class="table-header">
            <div class="round-number">第 {{ currentRound }} 局</div>
            <div class="record-link">桌面记录</div>
          </div>

          <div class="table-center">
            <div class="score-circle">
              <div class="score-value">{{ tableScore }}</div>
              <div class="score-label">桌面分</div>
            </div>
          </div>
        </div>

        <!-- 玩家列表区域 -->
        <div class="table-players-container">
          <div class="table-players">
            <div
              v-for="player in activePlayers"
              :key="player.id"
              class="table-player"
            >
              <div
                class="player-avatar"
                :class="getPlayerAvatarClass(player)"
                @click="showPlayerRecords(player)"
              >
                <avatar-display
                  style="width: 50px; height: 50px"
                  :avatarFileId="player.avatarFileId"
                  size="50px"
                />
              </div>
              <div class="player-info">
                <div class="player-name">{{ player.name }}</div>
                <div class="player-status">
                  {{ getPlayerRoundStats(player).distributeText }}
                </div>
                <div class="player-status">
                  {{ getPlayerRoundStats(player).collectText }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="bottom-actions">
        <button
          v-for="action in currentViewActions"
          :key="action.name"
          :class="['action-button', action.class]"
          @click="action.handler"
        >
          {{ action.name }}
        </button>
      </div>
    </div>

    <!-- 计分弹窗 -->
    <score-modal
      :visible="showScoreModal"
      :modalType="scoreModalType"
      :selectedPlayer="selectedPlayer"
      :currentUserId="realCurrentUserId"
      :players="activePlayers"
      @close="closeScoreModal"
      @confirm="handleScoreConfirm"
    />

    <!-- 房间排行榜弹窗 -->
    <room-ranking
      :visible="showRankingModal"
      :players="activePlayers"
      :teaWaterBalance="teaWaterBalance"
      @close="closeRankingModal"
    />

    <!-- 房间设置弹窗 -->
    <room-settings
      :visible="showSettingsModal"
      :soundEnabled="soundEnabled"
      :screenWakeLockEnabled="screenWakeLockEnabled"
      @close="closeSettingsModal"
      @toggle-sound="toggleSound"
      @toggle-screen-wake-lock="toggleScreenWakeLock"
    />

    <!-- 茶水设置弹窗 -->
    <tea-settings
      :visible="showTeaModal"
      :teaWaterLimitAmount="teaWaterLimitAmount"
      :teaWaterRatio="teaWaterRatio"
      @close="closeTeaSettings"
      @save="saveTeaSettings"
    />

    <!-- 邀请好友弹窗 -->
    <invite-friends
      v-if="roomInfo.id"
      :visible="showInviteModal"
      :roomId="roomInfo.id"
      :roomName="roomInfo.name"
      @close="closeInviteModal"
    />

    <!-- 用户资料编辑弹窗 -->
    <profile-editor
      v-if="showProfileEditorModal"
      :userData="currentUserData"
      @close="closeProfileEditor"
      @save="handleUserProfileSave"
    />

    <!-- 出分到桌面弹窗 -->
    <distribute-score
      :visible="showDistributeModal"
      @close="closeDistributeModal"
      @confirm="handleDistributeConfirm"
    />

    <!-- 收分弹窗 -->
    <collect-score
      :visible="showCollectModal"
      :tableScore="tableScore"
      @close="closeCollectModal"
      @confirm="handleCollectConfirm"
    />

    <!-- 桌面记录弹窗 -->
    <table-records
      :visible="showTableRecordsModal"
      :tableRecords="tableRecords"
      :currentRound="currentRound"
      :availableRounds="availableRounds"
      @close="closeTableRecordsModal"
      @round-change="handleRoundChange"
    />

    <!-- 个人记录弹窗 -->
    <player-records
      :visible="showPlayerRecordsModal"
      :selectedPlayer="selectedPlayerForRecords"
      :currentRound="currentRound"
      :playerRecords="playerRecords"
      @close="closePlayerRecordsModal"
    />

    <!-- 个人流水弹窗 -->
    <player-transactions
      :visible="showPlayerTransactionsModal"
      :selectedPlayer="selectedPlayerForTransactions"
      :playerTransactions="playerTransactions"
      @close="closePlayerTransactionsModal"
    />
  </div>
</template>

<script>
import TraditionalList from "@/components/traditional-list.vue";
import ProfileEditor from "@/components/profile-editor.vue";
import ScoreModal from "@/components/score-modal.vue";
import RoomRanking from "@/components/room-ranking.vue";
import RoomSettings from "@/components/room-settings.vue";
import TeaSettings from "@/components/tea-settings.vue";
import InviteFriends from "@/components/invite-friends.vue";
import DistributeScore from "@/components/distribute-score.vue";
import CollectScore from "@/components/collect-score.vue";
import TableRecords from "@/components/table-records.vue";
import PlayerRecords from "@/components/player-records.vue";
import PlayerTransactions from "@/components/player-transactions.vue";
import AvatarDisplay from "@/components/avatar-display.vue";

// 引入所有业务逻辑 mixins
import userProfileMixin from "./mixins/userProfileMixin.js";
import networkStatusMixin from "./mixins/networkStatusMixin.js";
import roomDataMixin from "./mixins/roomDataMixin.js";
import scoringMixin from "./mixins/scoringMixin.js";
import teaWaterMixin from "./mixins/teaWaterMixin.js";
import modalManageMixin from "./mixins/modalManageMixin.js";
import playerInteractionMixin from "./mixins/playerInteractionMixin.js";
import tableOperationMixin from "./mixins/tableOperationMixin.js";
import messageManageMixin from "./mixins/messageManageMixin.js";
import settingsManageMixin from "./mixins/settingsManageMixin.js";
import uiHelperMixin from "./mixins/uiHelperMixin.js";
import websocketMixin from "./mixins/websocketMixin.js";
import voiceMixin from "./mixins/voiceMixin.js";

export default {
  name: "RoomPage",
  mixins: [
    userProfileMixin,
    networkStatusMixin,
    roomDataMixin,
    scoringMixin,
    teaWaterMixin,
    modalManageMixin,
    playerInteractionMixin,
    tableOperationMixin,
    messageManageMixin,
    settingsManageMixin,
    uiHelperMixin,
    websocketMixin,
    voiceMixin,
  ],
  components: {
    TraditionalList,
    ProfileEditor,
    ScoreModal,
    RoomRanking,
    RoomSettings,
    TeaSettings,
    InviteFriends,
    DistributeScore,
    CollectScore,
    TableRecords,
    PlayerRecords,
    PlayerTransactions,
    AvatarDisplay,
  },
  data() {
    return {
      // 主页面只保留页面级别的数据，其他数据已迁移到对应的mixin中
    };
  },
  onShareAppMessage(res) {
    console.log("onShareAppMessage", res);
    console.log(this.roomInfo, 'roomInfo')
    // 确保参数存在且有效
    if (!this.roomInfo.id) {
      console.warn("分享失败：房间ID不存在");
      return {
        title: "随心打牌记分 - 好友聚会记分神器",
        path: "/pages/index/index",
        imageUrl:
          "https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/logo.png",
      };
    }

    // 构建带参数的分享路径
    const shareUrl = `/pages/join/index?roomId=${
      this.roomInfo.id
    }`;
    console.log("分享路径:", shareUrl);

    return {
      title: `邀请您加入${this.roomInfo.name || "房间"}`,
      path: shareUrl,
      // 可选：添加自定义分享图片
      imageUrl: 'https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/logo.png'
    };
  },
  computed: {
    // 主页面只保留页面级别的computed，其他computed已迁移到对应的mixin中

    /**
     * 过滤活跃玩家（未退出的玩家）
     * @returns {Array} 活跃玩家列表
     */
    activePlayers() {
      if (!this.players || !Array.isArray(this.players)) {
        return [];
      }
      
      // 过滤掉已退出的玩家
      const filteredPlayers = this.players.filter(player => !player.hasLeft);
      
      // 如果当前用户ID不存在，返回原列表
      if (!this.realCurrentUserId) {
        return filteredPlayers;
      }
      
      // 分离当前用户和其他玩家
      const currentUser = filteredPlayers.find(player => player.id === this.realCurrentUserId);
      const otherPlayers = filteredPlayers.filter(player => player.id !== this.realCurrentUserId);
      
      // 如果找到当前用户，将其放在第一位；否则返回原列表
      return currentUser ? [currentUser, ...otherPlayers] : filteredPlayers;
    },

    /**
     * 检查是否还能邀请更多玩家
     * 基于活跃玩家数量判断
     */
    canInviteMore() {
      return this.activePlayers.length < 8; // 房间最大8人
    },
  },

  watch: {
    // 主页面的watch已迁移到对应的mixin中
  },

  async onLoad(options) {
    await this.initializeRoom(options);
    uni.setNavigationBarTitle({
      title: this.roomInfo.name
    })
  },

  onShow() {
    // 页面显示时，只在特定条件下重新连接WebSocket
    // 1. 有房间信息且WebSocket配置完整
    // 2. 当前未连接且未在连接中
    // 3. 房间数据加载完成且无错误
    if (this.roomInfo?.id && 
        this.wsConfig?.roomId && 
        this.wsConfig.roomId === this.roomInfo.id &&
        !this.wsConnected && 
        !this.wsConnecting &&
        !this.isRoomDataLoading &&
        !this.roomDataLoadError) {
      console.log('页面显示，检测到合法的重连条件，尝试重新连接WebSocket');
      this.initWebSocket(this.roomInfo.id);
    } else {
      console.log('页面显示，跳过WebSocket重连', {
        hasRoomInfo: !!this.roomInfo?.id,
        hasWsConfig: !!this.wsConfig?.roomId,
        roomIdMatch: this.wsConfig?.roomId === this.roomInfo?.id,
        wsConnected: this.wsConnected,
        wsConnecting: this.wsConnecting,
        isLoading: this.isRoomDataLoading,
        hasError: !!this.roomDataLoadError
      });
    }
    
    // 恢复网络状态监听
    if (this.initNetworkStatus && this.roomInfo?.id) {
      this.initNetworkStatus();
    }
  },

  onHide() {
    // 页面隐藏时暂停WebSocket连接，避免后台接收消息
    console.log('页面隐藏，暂停WebSocket连接');
    if (this.wsConnected || this.wsConnecting) {
      // 使用暂停方法而不是关闭，保留配置用于恢复
      if (this.pauseWebSocket) {
        this.pauseWebSocket();
      } else {
        this.closeWebSocket();
      }
    }
    
    // 停止网络状态监听
    if (this.destroyNetworkStatus) {
      this.destroyNetworkStatus();
    }
  },

  onUnload() {
    // 页面卸载时彻底清理所有资源
    console.log('页面卸载，清理所有WebSocket资源');
    this.closeWebSocket();
    
    // 清理其他资源
    if (this.stopHintScrolling) {
      this.stopHintScrolling();
    }
    if (this.releaseWakeLock) {
      this.releaseWakeLock();
    }
    if (this.destroyNetworkStatus) {
      this.destroyNetworkStatus();
    }
  },

  mounted() {
    // 监听用户信息更新事件
    if (typeof uni !== "undefined" && uni.$on) {
      uni.$on("user-info-updated", this.onUserInfoUpdated);
    }
  },

  beforeDestroy() {
    // 清理事件监听
    if (typeof uni !== "undefined" && uni.$off) {
      uni.$off("user-info-updated", this.onUserInfoUpdated);
    }

    // 清理WebSocket连接
    if (this.closeWebSocket) {
      this.closeWebSocket();
    }
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
