<template>
  <div class="join-room-page">
    <!-- 顶部logo和房间号区域 -->
    <div class="header-section">
      <div class="logo-container">
        <image src="https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/logo.png" mode="aspectFit" class="logo-image"></image>
      </div>
      <div class="room-number-card">
        <div class="room-number-label">房间号</div>
        <div class="room-number-value">{{ roomInfo.roomNumber }}</div>
      </div>
    </div>

    <!-- 房间状态卡片 -->
    <div class="content-section">
      <!-- 加载中状态 -->
      <div class="status-card loading-status" v-if="isLoading">
        <div class="status-icon-container">
          <span class="loading-icon">⏳</span>
        </div>
        <div class="status-title">正在获取房间信息...</div>
        <div class="status-description">请稍候</div>
      </div>

      <!-- 错误状态 -->
      <div class="status-card error-status" v-else-if="loadError">
        <div class="status-icon-container">
          <span class="iconfont status-icon">&#xe881;</span>
        </div>
        <div class="status-title">获取房间信息失败</div>
        <div class="status-description">{{ loadError }}</div>
        <button class="retry-button" @click="retryFetchRoomInfo">重试</button>
      </div>

      <!-- 房间已关闭状态 -->
      <div class="status-card closed-status" v-else-if="!roomInfo.isActive">
        <div class="status-icon-container">
          <span class="iconfont status-icon">&#xe881;</span>
        </div>
        <div class="status-title">房间已关闭</div>
        <div class="status-description">你来晚了！房间已经关闭了</div>
      </div>

      <!-- 房间活跃状态 -->
      <div class="status-card active-status" v-else>
        <div class="members-header">
          <div class="members-count">房间内 {{ roomInfo.members.length }} / {{ roomInfo.maxPlayers }} 个成员</div>
          <div class="room-type">{{ roomInfo.gameType === 'classic' ? '传统玩法' : '给分玩法' }}</div>
        </div>
        <div class="members-container" v-if="roomInfo.members.length > 0">
          <div
            class="member-card"
            v-for="(member, index) in roomInfo.members"
            :key="index"
          >
            <div class="member-avatar-container">
              <avatar-display :avatarFileId="member.avatarFileId" size="60px" />
              <div class="creator-badge" v-if="member.isCreator">房主</div>
            </div>
            <div class="member-name">{{ member.name }}</div>
          </div>
        </div>
        <div class="empty-members" v-else>
          <div class="empty-text">暂无成员</div>
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <div class="action-section">
      <button
        class="join-button"
        :class="{ 'join-button--disabled': !roomInfo.isActive }"
        @click="joinRoom"
      >
        {{ roomInfo.isActive ? '确定加入房间' : '房间已关闭' }}
      </button>

      <div class="back-button" @click="goBack">
        <span class="back-text">返回主页</span>
      </div>
    </div>
  </div>
</template>

<script>
import { RoomService } from "@/utils/room.js";
import loginFun from "@/utils/login.js";
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  components: {
    AvatarDisplay,
  },
  data() {
    return {
      roomId: "",
      isLoading: false,
      loadError: null,
      roomInfo: {
        isActive: false,
        roomNumber: "",
        roomName: "",
        gameType: "",
        currentPlayers: 0,
        maxPlayers: 0,
        members: [],
      },
    };
  },
  onLoad(options) {
    console.log("join页面接收参数:", options);
    
    // 解析房间参数
    this.parseRoomParameters(options);
    
    // 检查用户注册状态
    if (!this.checkUserRegistrationStatus()) {
      return; // 如果用户未注册，直接返回，不继续执行
    }
    
    if (this.roomId) {
      this.fetchRoomInfo();
    } else {
      this.handleInvalidRoomId();
    }
  },
  methods: {
    /**
     * 检查用户注册状态
     * @returns {boolean} 是否已完善个人信息
     */
    checkUserRegistrationStatus() {
      // 检查登录状态
      if (!loginFun.checkLoginStatus()) {
        this.showUnregisteredUserModal("请先完善个人信息后再扫码加入房间");
        return false;
      }

      // 获取用户信息
      const userInfo = loginFun.storage.getUserInfo();
      
      // 检查用户是否已完善个人信息（主要是昵称）
      if (!userInfo || !userInfo.nickname || userInfo.nickname.trim() === "" || userInfo.nickname === "未设置昵称") {
        this.showUnregisteredUserModal("请先完善个人信息后再扫码加入房间");
        return false;
      }

      return true;
    },

    /**
     * 显示未注册用户提示框
     * @param {string} content 提示内容
     */
    showUnregisteredUserModal(content) {
      uni.showModal({
        title: "提示",
        content: content,
        showCancel: false,
        confirmText: "去完善",
        success: (res) => {
          if (res.confirm) {
            // 返回首页完善个人信息
            uni.reLaunch({
              url: "/pages/index/index"
            });
          }
        }
      });
    },

    /**
     * 解析房间参数 - 支持多种参数来源
     * @param {Object} options 页面启动参数
     */
    parseRoomParameters(options) {
      let roomId = null;
      
      // 1. 优先从页面参数获取
      roomId = options.roomId;
      
      // 2. 如果页面参数没有，尝试从小程序scene参数解析
      if (!roomId) {
        try {
          
          if (options && options.scene) {
            // 场景码1047表示扫描小程序码
            const sceneStr = decodeURIComponent(options.scene);
            console.log("解析scene参数:", sceneStr);
            
            // 解析 roomId=SX12345678 格式
            const roomIdMatch = sceneStr.match(/roomId=([^&]*)/);
            if (roomIdMatch && roomIdMatch[1]) {
              roomId = roomIdMatch[1];
              console.log("从scene解析到roomId:", roomId);
            }
          }
        } catch (error) {
          console.warn("解析小程序scene参数失败:", error);
        }
      }
      
      // 3. 验证房间ID格式
      if (roomId && /^SX[0-9]{8}$/.test(roomId)) {
        this.roomId = roomId;
        console.log("有效的房间ID:", roomId);
      } else {
        this.roomId = null;
        console.warn("无效的房间ID:", roomId);
      }
    },

    /**
     * 处理无效房间ID的情况
     */
    handleInvalidRoomId() {
      this.loadError = "二维码格式错误或房间参数无效";
      this.roomInfo.isActive = false;
      
      uni.showModal({
        title: "提示",
        content: "二维码格式错误或房间参数无效，请使用有效的房间二维码",
        showCancel: true,
        cancelText: "重新扫码",
        confirmText: "返回首页",
        success: (res) => {
          if (res.confirm) {
            // 返回首页
            uni.reLaunch({
              url: "/pages/index/index"
            });
          } else {
            // 重新扫码
            uni.navigateBack();
          }
        }
      });
    },

    // 获取房间信息
    async fetchRoomInfo() {
      if (!this.roomId) {
        this.handleInvalidRoomId();
        return;
      }

      try {
        console.log("正在获取房间信息:", this.roomId);
        this.isLoading = true;
        this.loadError = null;

        // 显示加载提示
        uni.showLoading({
          title: "获取房间信息...",
          mask: true,
        });

        // 调用真实API获取房间信息（预览模式）
        const result = await RoomService.getRoomInfo(this.roomId, true);

        if (result.success) {
          // 转换房间数据格式
          this.convertRoomData(result.data);
          console.log("房间信息获取成功:", this.roomInfo);
        } else {
          throw new Error(result.message || "获取房间信息失败");
        }
      } catch (error) {
        console.error("获取房间信息失败:", error);
        this.loadError = error.message || "获取房间信息失败";
        
        // 根据错误类型显示不同提示
        let errorMessage = "获取房间信息失败";
        if (error.message.includes("房间不存在")) {
          errorMessage = "房间不存在或已关闭";
          this.roomInfo.isActive = false;
        } else if (error.message.includes("无权限")) {
          errorMessage = "无权限访问该房间";
        } else if (error.message.includes("网络")) {
          errorMessage = "网络异常，请检查网络连接";
        }
        
        uni.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3000,
        });
      } finally {
        this.isLoading = false;
        uni.hideLoading();
      }
    },

    /**
     * 转换房间数据格式
     * @param {Object} roomData 来自API的房间数据
     */
    convertRoomData(roomData) {
      // 检查房间状态
      let isActive;
      if (roomData.isPreview) {
        // 预览模式：使用canJoin字段判断
        isActive = roomData.canJoin;
      } else {
        // 完整模式：使用房间状态判断
        isActive = roomData.roomStatus === "waiting" || roomData.roomStatus === "playing";
      }
      
      // 转换玩家数据格式（预览模式下可能没有players字段）
      let members = [];
      if (roomData.players && Array.isArray(roomData.players)) {
        members = roomData.players.map(player => ({
          name: player.nickname || "未知用户",
          avatarFileId: player.avatar_fileId || "",
          isCreator: player.is_creator || false
        }));
      }

      // 更新房间信息
      this.roomInfo = {
        isActive: isActive,
        roomNumber: roomData.roomName || `${this.roomId}号房`,
        roomName: roomData.roomName || "",
        gameType: roomData.gameType || "classic",
        currentPlayers: roomData.currentPlayers || 0,
        maxPlayers: roomData.maxPlayers || 8,
        members: members,
        canJoin: roomData.canJoin || false,
        isPreview: roomData.isPreview || false
      };

      console.log("房间数据转换完成:", this.roomInfo);
    },

    // 加入房间
    async joinRoom() {
      // 检查房间是否可以加入
      if (!this.roomInfo.isActive || (this.roomInfo.isPreview && !this.roomInfo.canJoin)) {
        let message = "房间已关闭，无法加入";
        if (this.roomInfo.currentPlayers >= this.roomInfo.maxPlayers) {
          message = "房间已满员，无法加入";
        }
        uni.showToast({
          title: message,
          icon: "none",
        });
        return;
      }

      try {
        // 检查登录状态
        if (!this.checkLoginStatus()) {
          return;
        }

        // 检查用户活跃房间冲突
        if (!await this.checkActiveRoomConflict()) {
          return;
        }

        console.log("正在加入房间:", this.roomId);
        
        // 显示加载状态
        uni.showLoading({
          title: "正在加入房间...",
          mask: true,
        });

        // 调用扫码加入房间API
        const roomObj = uniCloud.importObject('room');
        const token = loginFun.storage.getToken();
        const result = await roomObj.joinRoomByQR(token, this.roomId);

        if (result.code === 200) {
          if (result.data.already_in_room) {
            uni.showToast({
              title: "您已在房间中",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: "加入房间成功",
              icon: "success",
            });
          }

          // 延时跳转到房间页面
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/room/index?roomId=${this.roomId}&gameType=${this.roomInfo.gameType}`,
            });
          }, 1000);
        } else if (result.code === 409) {
          // 用户已有活跃房间
          uni.showModal({
            title: "提示",
            content: result.message,
            showCancel: false,
          });
        } else {
          throw new Error(result.message || "加入房间失败");
        }
      } catch (error) {
        console.error("加入房间失败:", error);
        uni.showToast({
          title: error.message || "加入房间失败，请重试",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },

    /**
     * 检查登录状态
     */
    checkLoginStatus() {
      if (!loginFun.checkLoginStatus()) {
        uni.showModal({
          title: "需要登录",
          content: "请先登录后再加入房间",
          confirmText: "去登录",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              // 跳转到首页进行登录
              uni.reLaunch({
                url: "/pages/index/index"
              });
            }
          }
        });
        return false;
      }
      return true;
    },

    /**
     * 检查用户活跃房间冲突
     */
    async checkActiveRoomConflict() {
      try {
        // 调用room云对象检查用户是否已在其他房间
        const roomObj = uniCloud.importObject('room');
        const token = loginFun.storage.getToken();
        const result = await roomObj.getUserActiveRoom(token);
        
        if (result.code === 200 && result.data && result.data.hasActiveRoom) {
          // 用户已有活跃房间
          uni.showModal({
            title: "提示",
            content: `您已在其他房间中，无法加入新房间。请先退出当前房间！`,
            showCancel: false,
          });
          return false;
        }
        
        return true;
      } catch (error) {
        // 检查失败时允许继续，避免阻塞正常流程
        console.warn("检查活跃房间失败:", error);
        return true;
      }
    },

    /**
     * 重试获取房间信息
     */
    retryFetchRoomInfo() {
      if (this.roomId) {
        this.fetchRoomInfo();
      } else {
        this.handleInvalidRoomId();
      }
    },

    // 返回主页
    goBack() {
      uni.reLaunch({
        url: "/pages/index/index",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.join-room-page {
  @include page-container;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: $spacing-lg $spacing-md;
  gap: $spacing-lg;
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;

  .logo-container {
    @include avatar(100px);
    margin-bottom: $spacing-lg;
    box-shadow: $box-shadow-base;

    .logo-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .room-number-card {
    @include card-shadow;
    padding: $spacing-md $spacing-lg;
    text-align: center;
    min-width: 240px;
    width: 100%;
    max-width: 400px;

    .room-number-label {
      font-size: $font-size-sm;
      color: $text-muted;
      margin-bottom: $spacing-xs;
    }

    .room-number-value {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      word-break: break-all;
    }
  }
}

.content-section {
  flex: 1;
  width: 100%;
  margin-bottom: $spacing-xl;

  .status-card {
    @include card-shadow;
    padding: $spacing-xl;
    text-align: center;
    margin-bottom: $spacing-base;

    &.loading-status {
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
      border-left: 4px solid #1890ff;

      .status-icon-container {
        margin-bottom: $spacing-lg;

        .loading-icon {
          font-size: 48px;
          animation: pulse 1.5s ease-in-out infinite alternate;
        }
      }

      .status-title {
        color: #1890ff;
        font-size: $font-size-xl;
        font-weight: $font-weight-semibold;
        margin-bottom: $spacing-sm;
      }

      .status-description {
        font-size: $font-size-base;
        color: $text-muted;
      }
    }

    &.error-status {
      background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
      border-left: 4px solid #e53e3e;

      .status-icon-container {
        margin-bottom: $spacing-lg;

        .status-icon {
          font-size: 60px;
          color: #e53e3e;
        }
      }

      .status-title {
        color: #e53e3e;
        font-size: $font-size-xl;
        font-weight: $font-weight-semibold;
        margin-bottom: $spacing-sm;
      }

      .status-description {
        font-size: $font-size-base;
        color: $text-muted;
        margin-bottom: $spacing-md;
      }

      .retry-button {
        padding: $spacing-xs $spacing-md;
        background-color: #e53e3e;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: $font-size-sm;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #c53030;
        }
      }
    }

    &.closed-status {

      .status-icon-container {
        margin-bottom: $spacing-lg;

        .status-icon {
          font-size: 60px;
          color: $accent-color;
        }
      }

      .status-title {
        font-size: $font-size-xl;
        font-weight: $font-weight-semibold;
        color: $text-primary;
        margin-bottom: $spacing-sm;
      }

      .status-description {
        font-size: $font-size-base;
        color: $text-muted;
        line-height: 1.5;
      }
    }

    &.active-status {

      .members-header {
        margin-bottom: $spacing-lg;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

        .members-count {
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          color: $text-primary;
        }

        .room-type {
          font-size: $font-size-sm;
          color: $text-secondary;
          background-color: #f0f0f0;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }

      .members-container {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: $spacing-lg;

        .member-card {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: $spacing-base;
          background-color: $background-color;
          border-radius: $border-radius-base;
          min-width: 80px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-sm;
          }

          .member-avatar-container {
            @include avatar(60px);
            margin-bottom: $spacing-sm;
            position: relative;

            .member-avatar {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .creator-badge {
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              background-color: #ff6b6b;
              color: white;
              font-size: 10px;
              padding: 2px 6px;
              border-radius: 8px;
              white-space: nowrap;
            }
          }

          .member-name {
            font-size: $font-size-sm;
            color: $text-secondary;
            text-align: center;
            word-break: break-all;
          }
        }
      }

      .empty-members {
        padding: $spacing-xl;
        
        .empty-text {
          font-size: $font-size-base;
          color: $text-muted;
          font-style: italic;
        }
      }
    }
  }
}

.action-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-lg;
  margin-top: auto;
  padding-top: $spacing-lg;

  .join-button {
    @include button-primary;
    width: 100%;
    max-width: 400px;
    height: 50px;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    border-radius: $border-radius-xl;
    box-shadow: $box-shadow-sm;
    transition: all 0.2s ease;

    &:active:not(.join-button--disabled) {
      transform: translateY(1px);
      box-shadow: none;
    }

    &.join-button--disabled {
      background-color: $border-color;
      color: $text-disabled;
      cursor: not-allowed;
      box-shadow: none;

      &:active {
        background-color: $border-color;
        transform: none;
      }
    }
  }

  .back-button {
    @include button-text;
    padding: $spacing-sm $spacing-md;

    .back-text {
      color: $text-secondary;
      font-size: $font-size-base;
      text-decoration: underline;
    }

    &:active {
      .back-text {
        color: $text-primary;
      }
    }
  }
}

// 增加一些响应式样式
@media (max-width: 750rpx) {
  .content-section {
    .status-card {
      &.active-status {
        .members-container {
          .member-card {
            min-width: 70px;

            .member-avatar-container {
              @include avatar(50px);
            }

            .member-name {
              font-size: $font-size-xs;
            }
          }
        }
      }
    }
  }
}

// 添加动画效果
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}
</style>

