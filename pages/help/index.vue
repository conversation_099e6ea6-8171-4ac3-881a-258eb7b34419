<template>
  <div class="help-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">帮助中心</div>
      <div class="header-subtitle">使用说明和常见问题</div>
    </div>

    <div class="help-content">
      <!-- 左侧导航 -->
      <div class="sidebar">
        <div 
          v-for="(item, index) in helpTopics" 
          :key="index"
          class="sidebar-item"
          :class="{ active: currentTopicIndex === index }"
          @click="setCurrentTopic(index)"
        >
          <div class="sidebar-item-text">{{ item.title }}</div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <scroll-view scroll-y="true" class="content-scroll">
          <div class="topic-content" v-if="currentTopic">
            <!-- 提示框 -->
            <div class="alert-box" v-if="currentTopic.alert">
              <div class="alert-icon">
                <span class="iconfont">&#xe884;</span>
              </div>
              <div class="alert-text">{{ currentTopic.alert }}</div>
            </div>

            <!-- 问题答案列表区域 -->
            <div class="qa-list" v-if="currentTopic.qaList">
              <div class="qa-item" v-for="(qa, index) in currentTopic.qaList" :key="index">
                <div class="question">
                  <span class="question-icon">Q</span>
                  <span class="question-text">{{ qa.question }}</span>
                </div>
                <div class="answer">
                  <span class="answer-icon">A</span>
                  <span class="answer-text">{{ qa.answer }}</span>
                </div>
                <!-- 每个QA项的独立图片 -->
                <div class="qa-image-section" v-if="qa.image">
                  <div class="image-container">
                    <image mode="aspectFit" :src="qa.image" class="help-image" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 全局图片说明区域（兼容性保留） -->
            <div class="image-section" v-if="currentTopic.image">
              <div class="image-container">
                <image :src="currentTopic.image" class="help-image" />
              </div>
            </div>
          </div>
        </scroll-view>
      </div>
    </div>

    <!-- 底部客服按钮 -->
    <div class="customer-service-section">
      <div class="service-hint">还有问题？我们来帮你解决</div>
      <div class="customer-service-button" @click="contactCustomerService">
        <span class="iconfont service-icon">&#xe884;</span>
        <span class="service-text">联系在线客服</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentTopicIndex: 0,
      helpTopics: [
        {
          title: '创建房间',
          alert: '注意：创建房间之前需要设置好头像和昵称哦',
          qaList: [
            {
              question: '如何创建房间?',
              answer: '打开小程序主页，点击"创建房间"按钮，选择房间类型即可。',
              image: 'https://env-00jxtnwq7mkk.normal.cloudstatic.cn/help/1.1.png'
            },
            {
              question: '创建房间需要什么条件？',
              answer: '需要完善个人资料，包括头像和昵称设置。',
              image: 'https://env-00jxtnwq7mkk.normal.cloudstatic.cn/help/1.2.png'
            },
            {
              question: '房间创建失败怎么办？',
              answer: '请检查网络连接，确保个人信息完整，然后重试创建。'
            },
            {
              question: '房间的类型有哪些？',
              answer: '房间的类型有：传统玩法和给分玩法，传统玩法就是类似麻将、斗地主等后计分的玩法，给分玩法就是类似德州扑克等先出分再收分的玩法。',
              image: 'https://env-00jxtnwq7mkk.normal.cloudstatic.cn/help/1.3.png'
            }
          ]
        },
        {
          title: '邀请朋友',
          qaList: [
            {
              question: '如何邀请朋友加入房间?',
              answer: '创建房间后，点击分享按钮，选择要分享的好友或群聊。'
            }
          ]
        },
        {
          title: '加入房间',
          qaList: [
            {
              question: '如何加入已有房间?',
              answer: '点击首页"扫码进房间"按钮，扫描房主分享的二维码即可加入。'
            }
          ]
        },
        {
          title: '退出房间',
          qaList: [
            {
              question: '如何退出当前房间?',
              answer: '在房间页面左下角，选择"退出房间"即可。'
            },
            {
              question: '退出房间后能重新加入吗？',
              answer: '可以通过再次扫描房间二维码加入，如果房间已经结束，则无法重新加入。'
            }
          ]
        },
        {
          title: '关于记分',
          qaList: [
            {
              question: '传统玩法如何记录分数?',
              answer: '在房间内点击其他玩家头像，在弹窗中填写分数后确定即可给某人计分，相当于输给某人多少分'
            },
            {
              question: '记错分数可以修改吗？',
              answer: '记错分数不可以修改，但是可以通让对方给你计分，抵消你的计分。'
            },
            {
              question: '支持哪些游戏类型？',
              answer: '目前支持传统累，比如麻将、扑克等或者给分类比如炸金花等多种游戏类型的计分，可以在创建房间时选择。'
            }
          ]
        },
        {
          title: '关于结算',
          qaList: [
            {
              question: '如何进行最终结算?',
              answer: '只有传统玩法需要结算，任何人都可以在分数不为0的情况下结算，结算代表着线下已结清，所以生成结算单后会把相关的分数清空'
            },
            {
              question: '结算是结算所有人吗？',
              answer: '只结算与你自己有关的分数。'
            }
          ]
        },
        {
          title: '关于茶水',
          qaList: [
            {
              question: '茶水费如何计算?',
              answer: '可以在创建房间时设置茶水费选项，系统会自动分配茶水费。'
            },
            {
              question: '茶水费由谁承担？',
              answer: '茶水费由茶水配置自动计算，理论上是按照一定比例分配，可以在结算单中查看。'
            }
          ]
        }
      ]
    };
  },
  computed: {
    currentTopic() {
      return this.helpTopics[this.currentTopicIndex];
    }
  },
  methods: {
    setCurrentTopic(index) {
      this.currentTopicIndex = index;
    },
    contactCustomerService() {
      // 实现联系客服功能
      uni.showToast({
        title: '正在连接客服...',
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.help-center {
  @include page-container;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  @include card;
  padding: $spacing-xl $spacing-lg $spacing-lg;
  margin-bottom: $spacing-base;
  text-align: center;
  
  .header-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }
  
  .header-subtitle {
    font-size: $font-size-sm;
    color: $text-muted;
  }
}

.help-content {
  flex: 1;
  @include card;
  display: flex;
  overflow: hidden;
  margin-bottom: $spacing-base;

  .sidebar {
    width: 120px;
    background-color: $background-color;
    border-right: 1px solid $border-color;
    overflow-y: auto;

    .sidebar-item {
      padding: $spacing-md;
      border-bottom: 1px solid $border-light;
      cursor: pointer;
      transition: all 0.2s ease;

      &.active {
        background-color: $primary-color;
        
        .sidebar-item-text {
          color: $text-white;
          font-weight: $font-weight-medium;
        }
      }

      &:not(.active):active {
        background-color: $primary-light;
      }

      .sidebar-item-text {
        font-size: $font-size-sm;
        color: $text-primary;
        text-align: center;
        line-height: 1.4;
      }
    }
  }

  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: $card-background;

    .content-scroll {
      flex: 1;
      width: 100%;
      height: calc(100vh - 280px); /* 减去页面头部、底部等固定元素的高度 */
      min-height: 300px;
      
      .topic-content {
        padding: $spacing-lg;
        height: calc(100vh - 280px);
        min-height: 300px;
      }
    }

    .alert-box {
      display: flex;
      align-items: flex-start;
      gap: $spacing-sm;
      background-color: $warning-color;
      background-color: rgba(255, 151, 106, 0.1);
      border: 1px solid rgba(255, 151, 106, 0.2);
      border-radius: $border-radius-base;
      padding: $spacing-md;
      margin-bottom: $spacing-lg;

      .alert-icon {
        color: $warning-color;
        font-size: $font-size-md;
        margin-top: 2px;
      }

      .alert-text {
        color: $warning-color;
        font-size: $font-size-sm;
        line-height: 1.5;
      }
    }

    .qa-list {
      .qa-item {
        margin-bottom: $spacing-xl;
        
        &:last-child {
          margin-bottom: 0;
        }

        .question {
          display: flex;
          align-items: flex-start;
          gap: $spacing-sm;
          margin-bottom: $spacing-md;

          .question-icon {
            @include icon-container(24px, $primary-color);
            width: 24px;
            height: 24px;
            font-size: $font-size-xs;
            font-weight: $font-weight-bold;
            color: $text-white;
            border-radius: $border-radius-round;
            flex-shrink: 0;
          }

          .question-text {
            font-size: $font-size-md;
            font-weight: $font-weight-medium;
            color: $text-primary;
            line-height: 1.5;
          }
        }

        .answer {
          display: flex;
          align-items: flex-start;
          gap: $spacing-sm;

          .answer-icon {
            @include icon-container(24px, $success-color);
            width: 24px;
            height: 24px;
            font-size: $font-size-xs;
            font-weight: $font-weight-bold;
            color: $text-white;
            border-radius: $border-radius-round;
            flex-shrink: 0;
          }

          .answer-text {
            color: $text-secondary;
            font-size: $font-size-base;
            line-height: 1.6;
          }
        }

        .qa-image-section {
          margin-top: $spacing-md;
          
          .image-container {
            border-radius: $border-radius-base;
            overflow: hidden;
            border: 1px solid $border-light;
            background-color: $background-color;
            padding: $spacing-sm;
            
            .help-image {
              width: 100%;
              display: block;
              border-radius: $border-radius-sm;
            }
          }
        }
      }
    }

    .image-section {
      margin-top: $spacing-lg;
      
      .image-container {
        border-radius: $border-radius-base;
        overflow: hidden;
        border: 1px solid $border-light;
        background-color: $background-color;
        padding: $spacing-sm;
        
        .help-image {
          width: 100%;
          display: block;
          border-radius: $border-radius-sm;
        }
      }
    }
  }
}

.customer-service-section {
  @include card;
  padding: $spacing-lg;
  text-align: center;

  .service-hint {
    font-size: $font-size-sm;
    color: $text-muted;
    margin-bottom: $spacing-md;
  }

  .customer-service-button {
    @include button-primary;
    width: 100%;
    padding: $spacing-md;
    gap: $spacing-sm;

    .service-icon {
      font-size: $font-size-lg;
    }

    .service-text {
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
    }

    &:active {
      background-color: $primary-dark;
    }
  }
}
</style>
