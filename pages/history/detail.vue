<template>
  <div class="history-detail">
    <!-- 顶部统计区域 -->
    <div class="summary-section">
      <scroll-view
        class="players-scroll"
        scroll-x="true"
        :show-scrollbar="false"
        :scroll-with-animation="true"
      >
        <div class="players-summary">
          <div
            v-for="(player, index) in summaryData"
            :key="index"
            class="player-stats"
          >
            <div class="player-avatar">
              <!-- 茶水特殊头像 -->
              <div
                v-if="player.id === 'tea_water'"
                class="tea-avatar-container"
              >
                <div class="avatar function-avatar tea-avatar">
                  <span class="iconfont function-icon">&#xe878;</span>
                </div>
              </div>
              <!-- 普通玩家头像 -->
              <avatar-display
                v-else
                class="avatar-image"
                :avatarFileId="player.avatarFileId"
                size="50px"
              />
            </div>
            <div class="player-name">{{ player.name }}</div>
            <div
              class="player-amount"
              :class="{
                positive: player.amount > 0,
                negative: player.amount < 0,
                zero: player.amount === 0,
              }"
            >
              {{ formatAmount(player.amount) }}
            </div>
          </div>
        </div>
      </scroll-view>
    </div>

    <!-- 历史详情列表 - 使用scroll-view替换div -->
    <scroll-view
      class="details-section"
      scroll-y="true"
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      :refresher-enabled="false"
      :show-scrollbar="true"
      :bounce="true"
    >
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="loadError" class="error-state">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ loadError }}</div>
        <div class="error-hint">将自动返回上一页</div>
      </div>

      <!-- 数据内容 -->
      <div v-else class="section-corver">
        <!-- 根据游戏类型选择不同的列表组件 -->
        <traditional-list 
          v-if="isTraditionalView"
          :list="detailsData"
          :currentUserId="currentUserId"
        ></traditional-list>
        
        <points-list 
          v-else
          :list="detailsData"
          :currentUserId="currentUserId"
        ></points-list>
      </div>

      <!-- 底部安全距离 -->
      <view class="safe-bottom"></view>
    </scroll-view>
  </div>
</template>

<script>
import TraditionalList from "@/components/traditional-list.vue";
import PointsList from "@/components/points-list.vue";
import AvatarDisplay from "@/components/avatar-display.vue";

import { RoomService } from "@/utils/room.js";
import loginFun from "@/utils/login.js";
import { safeAdd, safeMinus } from "@/utils/mathUtils.js";
export default {
  components: {
    TraditionalList,
    PointsList,
    AvatarDisplay,
  },
  data() {
    return {
      roomId: "",
      roomName: "",
      gameType: "classic", // 新增：房间游戏类型
      totalAmount: 0,
      // 摘要数据 - 顶部玩家统计
      summaryData: [],
      // 详情数据 - 历史记录列表
      detailsData: [],
      // 滚动位置
      scrollTop: 0,
      // 加载状态
      isLoading: false,
      loadError: null,
      // 当前用户ID
      currentUserId: "",
    };
  },
  computed: {
    // 是否为传统玩法
    isTraditionalView() {
      return this.gameType === "classic";
    },
  },
  onLoad(options) {
    console.log(options, 'options')
    // 获取房间ID参数
    if (options.roomId) {
      this.roomId = options.roomId;
      this.loadRealHistoryDetail();
    } else {
      // 参数不足，显示错误
      this.loadError = "缺少必要的房间ID参数";
    }
  },
  methods: {
    // 格式化金额显示，统一正负号
    formatAmount(amount) { 
      console.log(amount, 'amount')
      if (amount > 0) {
        return `+${amount}`;
      } else if (amount < 0) {
        return `${amount}`;
      } else {
        return "0";
      }
    },

    // 获取当前用户ID
    async getCurrentUserId() {
      try {
        const userResult = await loginFun.getUserInfo();
        if (userResult.success && userResult.data && userResult.data._id) {
          this.currentUserId = userResult.data._id;
          return userResult.data._id;
        } else {
          throw new Error("获取用户信息失败");
        }
      } catch (error) {
        console.error("获取当前用户ID失败:", error);
        return null;
      }
    },

    // 加载真实历史详情数据
    async loadRealHistoryDetail() {
      if (!this.roomId) {
        this.loadError = "房间ID不能为空";
        return;
      }

      try {
        this.isLoading = true;
        this.loadError = null;

        // 显示加载提示
        uni.showLoading({
          title: "加载中...",
          mask: true,
        });

        // 获取当前用户ID
        const userId = await this.getCurrentUserId();
        if (!userId) {
          throw new Error("用户未登录，请重新登录");
        }

        const roomResult = await RoomService.getRoomInfo(this.roomId);
        if (!roomResult.success) {
          throw new Error(roomResult.message || "获取房间信息失败");
        }

        const roomData = roomResult.data;
        this.roomName = roomData.roomName;
        this.gameType = roomData.gameType || "classic";

        // 获取房间消息
        const messagesResult = await RoomService.getMessages(this.roomId);
        if (!messagesResult.success) {
          throw new Error(messagesResult.message || "获取房间消息失败");
        }

        // 处理消息数据
        this.detailsData = messagesResult.data.messages || [];

        // 计算玩家汇总统计
        this.calculatePlayersSummary();

        uni.hideLoading();
      } catch (error) {
        console.error("加载历史详情失败:", error);
        this.loadError = error.message || "加载失败，请重试";
        
        uni.hideLoading();
        uni.showToast({
          title: this.loadError,
          icon: "none",
          duration: 3000,
        });

        // 延时返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 3000);
      } finally {
        this.isLoading = false;
      }
    },

    // 计算玩家分数汇总
    calculatePlayersSummary() {
      console.log(this.detailsData, 'this.detailsData')
      if (!this.detailsData || this.detailsData.length === 0) {
        this.summaryData = [];
        return;
      }

      // 用于存储每个玩家的分数统计
      const playerScores = {};
      // 茶水累计分数
      let teaWaterTotal = 0;

      // 遍历所有消息，计算每个玩家的最终分数
      this.detailsData.forEach(message => {
        if (this.isTraditionalView) {
          // 传统玩法：处理 score 类型消息
          if (message.type === 'score' && message.sender && message.target) {
            const senderId = message.sender.id;
            const targetId = message.target.id;
            const amount = Number(message.amount) || 0;
            const originalAmount = Number(message.original_amount) || amount;
            const teaAmount = Number(message.tea_amount) || 0;

            // 初始化玩家记录
            if (!playerScores[senderId]) {
              playerScores[senderId] = {
                id: senderId,
                name: message.sender.name,
                avatarFileId: message.sender.avatar_fileId,
                amount: 0
              };
            }
            if (!playerScores[targetId]) {
              playerScores[targetId] = {
                id: targetId,
                name: message.target.name,
                avatarFileId: message.target.avatar_fileId,
                amount: 0
              };
            }

            // 更新分数：发送者减少原始金额，接收者增加实际金额
            playerScores[senderId].amount = safeMinus(playerScores[senderId].amount, originalAmount);
            playerScores[targetId].amount = safeAdd(playerScores[targetId].amount, amount);
            
            // 累计茶水费
            if (teaAmount > 0) {
              teaWaterTotal = safeAdd(teaWaterTotal, teaAmount);
            }
          }
        } else {
          // 给分玩法：处理 score、distribute、collect 类型消息
          if (message.type === 'score' && message.sender && message.target) {
            // 给分玩法中的score消息（给分操作）
            const senderId = message.sender.id;
            const targetId = message.target.id;
            const amount = Number(message.amount) || 0;
            const originalAmount = Number(message.original_amount) || amount;
            const teaAmount = Number(message.tea_amount) || 0;

            // 初始化玩家记录
            if (!playerScores[senderId]) {
              playerScores[senderId] = {
                id: senderId,
                name: message.sender.name,
                avatarFileId: message.sender.avatar_fileId,
                amount: 0
              };
            }
            if (!playerScores[targetId]) {
              playerScores[targetId] = {
                id: targetId,
                name: message.target.name,
                avatarFileId: message.target.avatar_fileId,
                amount: 0
              };
            }

            // 更新分数：发送者减少原始金额，接收者增加实际金额
            playerScores[senderId].amount = safeMinus(playerScores[senderId].amount, originalAmount);
            playerScores[targetId].amount = safeAdd(playerScores[targetId].amount, amount);
            
            // 累计茶水费
            if (teaAmount > 0) {
              teaWaterTotal = safeAdd(teaWaterTotal, teaAmount);
            }
          } else if (message.type === 'distribute' && message.sender) {
            const senderId = message.sender.id;
            const amount = Number(message.amount) || 0;

            if (!playerScores[senderId]) {
              playerScores[senderId] = {
                id: senderId,
                name: message.sender.name,
                avatarFileId: message.sender.avatar_fileId,
                amount: 0
              };
            }

            // 出分：玩家分数减少
            playerScores[senderId].amount = safeMinus(playerScores[senderId].amount, amount);
          } else if (message.type === 'collect' && message.sender) {
            const senderId = message.sender.id;
            const actualAmount = Number(message.actual_amount) || Number(message.amount) || 0;
            const teaAmount = Number(message.tea_amount) || 0;

            if (!playerScores[senderId]) {
              playerScores[senderId] = {
                id: senderId,
                name: message.sender.name,
                avatarFileId: message.sender.avatar_fileId,
                amount: 0
              };
            }

            // 收分：玩家分数增加（使用实际到账金额）
            playerScores[senderId].amount = safeAdd(playerScores[senderId].amount, actualAmount);
            
            // 累计茶水费
            if (teaAmount > 0) {
              teaWaterTotal = safeAdd(teaWaterTotal, teaAmount);
            }
          }
        }
      });

      // 转换为数组
      let summaryArray = Object.values(playerScores);
      
      // 按分数降序排序（仅玩家）
      summaryArray.sort((a, b) => b.amount - a.amount);
      
      // 如果有茶水费，添加茶水到最后（传统玩法和给分玩法都显示）
      if (teaWaterTotal > 0) {
        summaryArray.push({
          id: 'tea_water',
          name: '茶水',
          avatarFileId: null, // 茶水头像特殊处理
          amount: teaWaterTotal
        });
      }
      
      this.summaryData = summaryArray;
      console.log(this.summaryData, 'this.summaryData')
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.history-detail {
  @include page-container;
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 0;
}

// 顶部摘要区域样式
.summary-section {
  @include card;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: $spacing-lg 0;
  margin: 0;
  border-radius: 0;
  box-shadow: $box-shadow-base;
  border-bottom: 1px solid $border-color;

  .players-scroll {
    width: 100%;
    white-space: nowrap;
  }

  .players-summary {
    display: inline-flex;
    min-width: 100%;
    padding: 0 $spacing-md;
    box-sizing: border-box;

    .player-stats {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      width: 160rpx;
      margin: 0 $spacing-sm;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }

      .player-avatar {
        @include avatar(80rpx);
        margin-bottom: $spacing-base;
        border: 2rpx solid $border-light;
        transition: all 0.3s ease;
        box-shadow: $box-shadow-sm;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        // 茶水头像容器
        .tea-avatar-container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .avatar {
            width: 80rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2rpx solid transparent;
            transition: all 0.3s ease;
            
            &.function-avatar {
              background: $primary-light;
              border-color: $border-color;
              
              &.tea-avatar {
                background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
                border-color: #f59e0b;
              }
            }
            
            .function-icon {
              font-size: 36rpx;
              color: $text-secondary;
            }
          }
        }
      }

      .player-name {
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
        text-align: center;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: $font-weight-medium;
      }

      .player-amount {
        font-size: $font-size-md;
        font-weight: $font-weight-semibold;
        text-align: center;
        padding: $spacing-xs $spacing-base;
        border-radius: $border-radius-lg;
        min-width: 80rpx;
        background: $background-color;
        border: 1rpx solid $border-color;
        transition: all 0.3s ease;
        box-shadow: $box-shadow-sm;

        &.positive {
          color: $positive-amount-color;
          background: rgba(250, 81, 81, 0.1);
          border-color: rgba(250, 81, 81, 0.3);
        }

        &.negative {
          color: $negative-amount-color;
          background: rgba(7, 193, 96, 0.1);
          border-color: rgba(7, 193, 96, 0.3);
        }

        &.zero {
          color: $text-muted;
          background: $background-color;
          border-color: $border-color;
        }
      }
    }
  }
}

// 详情列表区域样式
.details-section {
  margin-top: 260rpx;
  flex: 1;
  height: calc(100vh - 260rpx);
  background-color: $background-color;

  .section-corver {
    padding: $spacing-md;
    box-sizing: border-box;
  }
}

.safe-bottom {
  height: $spacing-xl;
}

// 加载状态样式
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-xxl 0;
  
  .loading-text {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}

// 错误状态样式
.error-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $spacing-xxl $spacing-lg;
  text-align: center;
  
  .error-icon {
    font-size: 48px;
    margin-bottom: $spacing-md;
  }
  
  .error-text {
    font-size: $font-size-base;
    color: $text-primary;
    margin-bottom: $spacing-sm;
    font-weight: $font-weight-medium;
  }
  
  .error-hint {
    font-size: $font-size-sm;
    color: $text-muted;
  }
}
</style>
