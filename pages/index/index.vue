<template>
  <div class="chessAndCard_scoring-page">
    <!-- 用户信息区域 -->
    <div class="user-profile" @click="showProfileEditor">
      <div class="profile-left">
        <div class="avatar">
          <avatar-display
            style="width: 50px; height: 50px"
            :avatarFileId="userData.avatar_fileId"
            size="50px"
          />
        </div>
        <div class="user-info">
          <div class="username">{{ userData.username || "未设置昵称" }}</div>
          <div class="edit-hint">点击编辑个人资料</div>
        </div>
      </div>
      <div class="arrow-right">
        <span class="iconfont edit-icon">&#xe87f;</span>
      </div>
    </div>

    <!-- 统计数据区域 -->
    <div class="stats-container">
      <div class="stat-item" @click="viewHistory">
        <div class="stat-value">{{ statsData.wins }}</div>
        <div class="stat-label">胜场</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item" @click="viewHistory">
        <div class="stat-value">{{ statsData.losses }}</div>
        <div class="stat-label">负场</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item" @click="viewHistory">
        <div class="stat-value">{{ calculateWinRate }}%</div>
        <div class="stat-label">胜率</div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-actions">
      <div class="action-button primary-button" @click="createRoom">
        <div class="button-icon">
          <span class="iconfont create-icon">&#xe87b;</span>
        </div>
        <div class="button-text">创建房间</div>
      </div>
      <div class="action-button secondary-button" @click="scanCode">
        <div class="button-icon">
          <span class="iconfont scan-icon">&#xe87c;</span>
        </div>
        <div class="button-text">扫码加入</div>
      </div>
    </div>

    <!-- 功能列表区域 -->
    <div class="function-list">
      <div class="function-item" @click="viewHistory">
        <div class="function-icon">
          <span class="iconfont paihang-icon">&#xe875;</span>
        </div>
        <div class="function-content">
          <div class="function-title">历史记录</div>
          <div class="function-desc">{{ statsData.historyRooms }}个房间</div>
        </div>
        <div class="arrow-right">
          <span class="iconfont more-icon">&#xe883;</span>
        </div>
      </div>

      <div class="function-item" @click="downloadSign">
        <div class="function-icon">
          <span class="iconfont biaopai-icon">&#xe87e;</span>
        </div>
        <div class="function-content">
          <div class="function-title">桌面标牌</div>
          <div class="function-desc">下载打印标牌</div>
        </div>
        <div class="arrow-right">
          <span class="iconfont more-icon">&#xe883;</span>
        </div>
      </div>

      <div class="function-item" @click="openHelpCenter">
        <div class="function-icon">
          <span class="iconfont wenhao-icon">&#xe8841;</span>
        </div>
        <div class="function-content">
          <div class="function-title">帮助中心</div>
          <div class="function-desc">使用说明和常见问题</div>
        </div>
        <div class="arrow-right">
          <span class="iconfont more-icon">&#xe883;</span>
        </div>
      </div>
    </div>

    <!-- 底部评分区域 -->
    <div class="rating-section" @click="rateApp">
      <span class="rating-text"
        >觉得好用？给个好评吧
        <span class="iconfont zan-icon">&#xe87d;</span>
      </span>
    </div>

    <!-- 进行中房间提示 -->
    <div
      class="active-room-section"
      v-if="activeRoom.isActive"
      @click="enterActiveRoom"
    >
      <div class="room-info">
        <div class="room-status-badge">进行中</div>
        <div class="room-text">
          正在 <span class="room-name">{{ activeRoom.name }}</span>
        </div>
        <div class="room-time">已创建 {{ activeRoom.timeDisplay }}</div>
      </div>
      <div class="enter-room-btn">进入</div>
    </div>

    <!-- 使用用户信息编辑弹窗组件 -->
    <profile-editor
      v-if="showModal"
      :userData="userData"
      @close="closeModal"
      @save="handleUserProfileSave"
    />

    <!-- 房间玩法选择弹窗 -->
    <div
      class="game-style-modal"
      v-if="showGameStyleModal"
      @click.self="closeGameStyleModal"
    >
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">选择房间玩法</div>
          <div class="close-button" @click="closeGameStyleModal">
            <span class="iconfont">✕</span>
          </div>
        </div>
        <div class="game-style-options">
          <div class="game-style-option" @click="selectGameStyle('classic')">
            <div class="option-icon">
              <span class="iconfont classic-icon">&#xe873;</span>
            </div>
            <div class="option-title">经典玩法</div>
            <div class="option-description">
              先有输赢再付账<br />如：麻将、斗地主
            </div>
          </div>
          <div class="game-style-option" @click="selectGameStyle('points')">
            <div class="option-icon">
              <span class="iconfont score-icon">&#xe877;</span>
            </div>
            <div class="option-title">给分玩法</div>
            <div class="option-description">
              先押分再有输赢<br />如：牛牛、金花
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ProfileEditor from "@/components/profile-editor.vue";
import AvatarDisplay from "@/components/avatar-display.vue";
import loginFun from "@/utils/login.js";

export default {
  name: "chessAndCard_scoring",
  components: {
    ProfileEditor,
    AvatarDisplay,
  },
  data() {
    return {
      showModal: false,
      showGameStyleModal: false,
      userData: {
        username: "",
        avatar_fileId: "",
      },
      statsData: {
        wins: 0,
        losses: 0,
        historyRooms: 0,
      },
      activeRoom: {
        isActive: false,
        room_id: "",
        name: "",
        game_type: "",
        room_status: "",
        timeDisplay: "",
        current_players: 0,
        max_players: 8,
        participants: [],
      },
      isLoading: false,
      loginInProgress: false,
    };
  },
  computed: {
    calculateWinRate() {
      const total = this.statsData.wins + this.statsData.losses;
      if (total === 0) return 0;
      return Math.round((this.statsData.wins / total) * 100);
    },
  },
  mounted() {
    // 页面加载时自动登录
    this.autoLogin();

    // 监听退出房间后的数据刷新事件
    uni.$on("refreshUserData", this.forceRefreshUserData);
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off("refreshUserData", this.forceRefreshUserData);
  },
  onShow() {
    // 页面显示时刷新用户信息
    this.refreshUserInfo();
  },
  onLoad(options) {
    // 处理分享参数
    this.handleShareParams(options);
  },
  onShareAppMessage(options) {
    // 处理小程序分享
    return this.handleAppShare(options);
  },
  methods: {
    /**
     * 检查用户活跃房间状态
     */
    async checkActiveRoom() {
      try {
        // 检查是否已登录
        if (!loginFun.checkLoginStatus()) {
          this.activeRoom.isActive = false;
          return;
        }

        const token = loginFun.storage.getToken();
        if (!token) {
          this.activeRoom.isActive = false;
          return;
        }

        // 调用room云对象获取活跃房间
        const roomObj = uniCloud.importObject("room");
        const result = await roomObj.getUserActiveRoom(token);

        if (result.code === 200 && result.data.hasActiveRoom) {
          // 有活跃房间，更新显示
          this.updateActiveRoomDisplay(result.data.activeRoom);
        } else {
          // 无活跃房间
          this.activeRoom.isActive = false;
        }
      } catch (error) {
        console.error("检查活跃房间失败:", error);
        this.activeRoom.isActive = false;
      }
    },

    /**
     * 刷新用户信息
     * 优先使用本地缓存，token过期时才从云端获取
     */
    async refreshUserInfo() {
      try {
        // 检查登录状态和token有效性
        if (loginFun.checkLoginStatus()) {
          // 已登录且token有效，尝试获取本地用户信息
          const localUserInfo = loginFun.storage.getUserInfo();
          if (localUserInfo) {
            // 有本地用户信息，直接更新界面
            this.updateUserDataFromCloud(localUserInfo);
            // 检查活跃房间状态
            await this.checkActiveRoom();

            // 异步从云端获取最新数据，确保统计数据准确性
            try {
              const result = await loginFun.getUserInfo();
              if (result.success) {
                // 如果云端数据与本地数据不同，更新界面
                const cloudData = result.data;
                if (
                  cloudData.win_games !== localUserInfo.win_games ||
                  cloudData.lose_games !== localUserInfo.lose_games ||
                  cloudData.participated_rooms !==
                    localUserInfo.participated_rooms
                ) {
                  console.log("检测到统计数据变化，更新界面");
                  this.updateUserDataFromCloud(cloudData);
                }
              }
            } catch (bgError) {
              // 后台获取失败不影响主流程
              console.warn("后台获取用户数据失败:", bgError);
            }

            return;
          }

          // 没有本地用户信息，从云端获取
          const result = await loginFun.getUserInfo();
          if (result.success) {
            this.updateUserDataFromCloud(result.data);
            // 检查活跃房间状态
            await this.checkActiveRoom();
          }
        }
        // 如果未登录或token过期，不执行任何操作
        // 避免在onShow中触发登录流程，保持用户体验流畅
      } catch (error) {
        console.warn("刷新用户信息失败:", error);
        // 静默失败，不影响页面正常使用
      }
    },

    /**
     * 自动登录
     */
    async autoLogin() {
      if (this.loginInProgress) return;

      this.loginInProgress = true;
      this.isLoading = true;

      try {
        // 首先检查本地是否有有效的登录状态
        if (loginFun.checkLoginStatus()) {
          // 有有效登录状态，尝试获取用户信息
          const result = await loginFun.getUserInfo();
          if (result.success) {
            // 获取用户信息成功，更新界面
            this.updateUserDataFromCloud(result.data);
            return;
          }
        }

        // 本地没有有效登录状态，尝试微信登录
        const loginResult = await loginFun.login();

        if (loginResult.success) {
          const { userInfo, isNewUser } = loginResult.data;
          console.log(userInfo, "userInfo");
          if (isNewUser) {
            // 如果是新用户，展示默认登录随机的昵称
            this.userData.username = userInfo.nickname;

            // 新用户，显示编辑个人资料弹窗
            // console.log("检测到新用户，显示个人资料编辑框");
            console.log("新用户");
            // this.showProfileEditor();
          } else {
            // 老用户，直接更新用户数据并显示
            console.log("老用户自动登录成功");
            this.updateUserDataFromCloud(userInfo);
          }
        } else {
          // 登录失败，显示编辑个人资料弹窗
          console.log("自动登录失败:", loginResult.message);
          this.showProfileEditor();
        }
      } catch (error) {
        console.error("自动登录异常:", error);
        this.showProfileEditor();
      } finally {
        this.isLoading = false;
        this.loginInProgress = false;
      }
    },

    /**
     * 从云端数据更新本地用户数据
     */
    updateUserDataFromCloud(cloudUserData) {
      if (!cloudUserData) return;

      // 更新用户基本信息
      this.userData = {
        username: cloudUserData.nickname || "未设置昵称",
        avatar_fileId: cloudUserData.avatar_fileId || "",
        signature: cloudUserData.signature || "",
        gender: cloudUserData.gender || 0,
      };
      // 更新统计数据 - 直接从用户数据读取，字段名匹配数据库schema
      this.statsData = {
        wins: cloudUserData.win_games || 0,
        losses: cloudUserData.lose_games || 0,
        historyRooms: cloudUserData.participated_rooms || 0, // 使用参与房间数而不是创建房间数
      };
    },

    showProfileEditor() {
      this.showModal = true;
    },

    closeModal() {
      this.showModal = false;
    },

    /**
     * 处理用户资料保存
     */
    async handleUserProfileSave(userData) {
      try {
        uni.showLoading({ title: "保存中..." });

        // 检查登录状态
        if (!loginFun.checkLoginStatus()) {
          // 未登录，先进行登录
          const loginResult = await loginFun.login();
          if (!loginResult.success) {
            throw new Error(loginResult.message || "登录失败");
          }
        }

        // 更新云端用户信息
        const updateResult = await loginFun.updateUserInfo({
          nickname: userData.username,
          avatar_fileId: userData.avatar_fileId,
          signature: userData.signature,
          gender: userData.gender,
        });

        if (updateResult.success) {
          // 更新成功，使用返回的数据更新本地UI
          if (updateResult.data) {
            this.updateUserDataFromCloud(updateResult.data);
          }

          uni.showToast({
            title: "保存成功",
            icon: "success",
          });
        } else {
          throw new Error(updateResult.message || "保存失败");
        }
      } catch (error) {
        console.error("保存用户资料失败:", error);

        // 云端保存失败时的错误提示
        uni.showToast({
          title: error.message || "保存失败，请重试",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },

    async scanCode() {
      // 检查是否存在活跃房间
      if (this.activeRoom.isActive) {
        uni.showModal({
          title: "提示",
          content: `您正在${this.activeRoom.name}，无法创建或加入其他房间。请进入房间后主动退出后再来加入或者创建吧！`,
          showCancel: false,
        });
        return;
      }

      // 检查登录状态
      if (!loginFun.checkLoginStatus()) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        this.showProfileEditor();
        return;
      }

      // 扫码进入房间功能
      uni.scanCode({
        success: async (res) => {
          console.log("扫码结果:", res);
          await this.handleScanResult(res.result);
        },
        fail: (err) => {
          console.error("扫码失败:", err);
          uni.showToast({
            title: "扫码失败，请重试",
            icon: "none",
          });
        },
      });
    },

    /**
     * 处理扫码结果
     */
    async handleScanResult(scanResult) {
      try {
        let roomId = null;

        // 尝试解析不同格式的扫码结果
        if (typeof scanResult === "string") {
          // 直接是房间ID
          if (/^SX[0-9]{8}$/.test(scanResult)) {
            roomId = scanResult;
          } else {
            // 可能包含其他格式，尝试提取房间ID
            const roomIdMatch = scanResult.match(/SX[0-9]{8}/);
            if (roomIdMatch) {
              roomId = roomIdMatch[0];
            }
          }
        }

        if (!roomId) {
          uni.showToast({
            title: "无效的房间二维码",
            icon: "none",
          });
          return;
        }

        // 显示加载状态
        uni.showLoading({ title: "加入房间中..." });

        // 调用房间云对象加入房间
        const roomObj = uniCloud.importObject("room");
        const token = loginFun.storage.getToken();
        const result = await roomObj.joinRoomByQR(token, roomId);

        if (result.code === 200) {
          if (result.data.already_in_room) {
            uni.showToast({
              title: "您已在房间中",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: "加入房间成功",
              icon: "success",
            });
          }

          // 更新活跃房间状态
          await this.checkActiveRoom();

          // 导航到房间页面
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/room/index?roomId=${roomId}&gameType=${result.data.game_type}`,
            });
          }, 1500);
        } else if (result.code === 409) {
          // 用户已有活跃房间
          uni.showModal({
            title: "提示",
            content: result.message,
            showCancel: false,
          });
          // 更新活跃房间显示
          await this.checkActiveRoom();
        } else {
          throw new Error(result.message || "加入房间失败");
        }
      } catch (error) {
        console.error("处理扫码结果失败:", error);
        uni.showToast({
          title: error.message || "加入房间失败，请重试",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },

    async createRoom() {
      // 检查是否存在活跃房间
      if (this.activeRoom.isActive) {
        uni.showModal({
          title: "提示",
          content: `您正在${this.activeRoom.name}，无法创建或加入其他房间。请进入房间后主动退出后再来加入或者创建吧！`,
          showCancel: false,
        });
        return;
      }

      // 检查登录状态
      if (!loginFun.checkLoginStatus()) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        this.showProfileEditor();
        return;
      }
      // 检查个人资料完整性
      if (!this.userData.username || !this.userData.avatar_fileId) {
        uni.showToast({
          title: "请先设置个人资料",
          icon: "none",
        });
        this.showProfileEditor();
        return;
      }

      // 先检查是否有活跃房间（实时检查）
      await this.checkActiveRoom();
      if (this.activeRoom.isActive) {
        uni.showModal({
          title: "提示",
          content: `您正在${this.activeRoom.name}，无法创建新房间。请先退出当前房间！`,
          showCancel: false,
        });
        return;
      }

      // 显示游戏玩法选择弹窗
      this.showGameStyleModal = true;
    },

    createNewRoom() {},

    viewHistory() {
      // 查看历史房间功能
      uni.navigateTo({
        url: "/pages/history/index",
      });
    },

    openHelpCenter() {
      // 打开帮助中心
      uni.navigateTo({
        url: "/pages/help/index",
      });
    },

    contactCustomerService() {
      // 联系客服
    },

    downloadSign() {
      // 下载茶馆桌面标牌
      uni.navigateTo({
        url: "/pages/download/index",
      });
    },

    rateApp() {
      // 评分功能
    },

    enterActiveRoom() {
      // 进入当前活跃房间
      if (!this.activeRoom.isActive) {
        uni.showToast({
          title: "当前无活跃房间",
          icon: "none",
        });
        return;
      }

      uni.showToast({
        title: "正在进入房间...",
        icon: "none",
      });

      // 导航到房间页面
      uni.navigateTo({
        url: `/pages/room/index?roomId=${this.activeRoom.room_id}&gameType=${this.activeRoom.game_type}`,
      });
    },

    /**
     * 更新活跃房间显示
     */
    updateActiveRoomDisplay(activeRoomData) {
      this.activeRoom = {
        isActive: true,
        room_id: activeRoomData.room_id,
        name: activeRoomData.room_name,
        game_type: activeRoomData.game_type,
        room_status: activeRoomData.room_status,
        timeDisplay: activeRoomData.timeDisplay,
        current_players: activeRoomData.current_players,
        max_players: activeRoomData.max_players,
        participants: [], // 保持原有格式，暂时为空数组
      };
    },

    closeGameStyleModal() {
      this.showGameStyleModal = false;
    },

    async selectGameStyle(style) {
      // 处理选择房间玩法
      console.log("Selected game style:", style);

      // 根据玩法类型设置确认信息
      const gameStyleInfo = {
        classic: {
          title: "确认创建经典玩法房间？",
          content: "经典玩法：先有输赢再付账，如麻将、斗地主等",
        },
        points: {
          title: "确认创建给分玩法房间？",
          content: "给分玩法：先押分再有输赢，如牛牛、金花等",
        },
      };

      const currentStyle = gameStyleInfo[style];

      // 显示二次确认弹窗
      uni.showModal({
        title: currentStyle.title,
        content: currentStyle.content,
        confirmText: "确认创建",
        cancelText: "取消",
        success: async (res) => {
          if (res.confirm) {
            // 用户确认，开始创建房间
            await this.handleCreateRoom(style);
          }
          // 如果用户取消，不做任何操作，保持玩法选择弹窗开启状态
        },
      });
    },

    /**
     * 处理房间创建
     */
    async handleCreateRoom(gameType) {
      try {
        uni.showLoading({ title: "创建房间中..." });

        const token = loginFun.storage.getToken();
        if (!token) {
          throw new Error("登录状态已失效，请重新登录");
        }

        // 调用room云对象创建房间
        const roomObj = uniCloud.importObject("room");
        const result = await roomObj.createRoom(token, gameType);

        if (result.code === 200) {
          // 创建成功
          uni.showToast({
            title: "房间创建成功",
            icon: "success",
          });

          // 关闭玩法选择弹窗
          this.showGameStyleModal = false;

          // 更新活跃房间状态
          await this.checkActiveRoom();

          // 导航到房间页面
          uni.navigateTo({
            url: `/pages/room/index?roomId=${result.data.room_id}&gameType=${gameType}`,
          });
        } else if (result.code === 409) {
          // 用户已有活跃房间
          uni.showModal({
            title: "提示",
            content: result.message,
            showCancel: false,
          });
          // 更新活跃房间显示
          await this.checkActiveRoom();
        } else {
          throw new Error(result.message || "创建房间失败");
        }
      } catch (error) {
        console.error("创建房间失败:", error);
        uni.showToast({
          title: error.message || "创建房间失败，请重试",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },

    /**
     * 处理分享参数
     */
    async handleShareParams(options) {
      if (options && options.roomId) {
        console.log("检测到分享参数，房间ID:", options.roomId);

        // 延迟处理，确保页面完全加载
        setTimeout(async () => {
          // 检查登录状态
          if (!loginFun.checkLoginStatus()) {
            // 未登录时先显示登录提示
            uni.showModal({
              title: "需要登录",
              content: "请先登录后再加入房间",
              confirmText: "去登录",
              success: (res) => {
                if (res.confirm) {
                  this.showProfileEditor();
                }
              },
            });
            return;
          }

          // 已登录，尝试加入房间
          await this.handleScanResult(options.roomId);
        }, 1000);
      }
    },

    /**
     * 处理小程序分享
     */
    handleAppShare(options) {
      console.log("handleAppShare", options);
      // 默认分享内容
      let shareInfo = {
        title: "随心打牌记分 - 好友聚会记分神器",
        path: "/pages/index/index",
        imageUrl:
          "https://env-00jxtnwq7mkk.normal.cloudstatic.cn/images/logo.png",
      };

      // 如果是从邀请好友组件触发的分享，自定义分享内容
      // if (options.from === "invite-friends" && options.target) {
      //   const { roomId, roomName } = options.target.dataset;
      //   if (roomId && roomName) {
      //     shareInfo = {
      //       title: `邀请您加入${roomName}`,
      //       path: `/pages/index/index?roomId=${roomId}`,
      //       imageUrl: "", // 可以设置房间相关的分享图片
      //     };
      //   }
      // }

      // console.log("分享信息:", shareInfo);
      return shareInfo;
    },

    /**
     * 强制刷新用户数据（退出房间后调用）
     */
    async forceRefreshUserData() {
      try {
        console.log("收到退出房间事件，强制刷新用户数据");

        // 检查登录状态
        if (!loginFun.checkLoginStatus()) {
          console.log("用户未登录，跳过数据刷新");
          return;
        }

        // 强制从云端获取最新用户信息
        const result = await loginFun.getUserInfo();
        if (result.success) {
          console.log("强制刷新用户数据成功");
          this.updateUserDataFromCloud(result.data);
          // 同时检查活跃房间状态
          await this.checkActiveRoom();
        } else {
          console.warn("强制刷新用户数据失败:", result.message);
        }
      } catch (error) {
        console.error("强制刷新用户数据异常:", error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/common.scss";

.chessAndCard_scoring-page {
  @include page-container;
  padding-bottom: v-bind('activeRoom.isActive ? "80px" : "20px"');
}

.user-profile {
  @include list-item;
  justify-content: space-between;
  border-bottom: 1px solid $border-color;

  .profile-left {
    display: flex;
    align-items: center;
    gap: $spacing-base;

    .avatar {
      @include avatar(50px);
    }

    .user-info {
      .username {
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: 2px;
      }

      .edit-hint {
        font-size: $font-size-sm;
        color: $text-muted;
      }
    }
  }

  .arrow-right {
    color: $text-muted;
    font-size: $font-size-sm;
  }
}

.stats-container {
  @include card;
  padding: $spacing-lg 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: $spacing-base;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-xs;

    .stat-value {
      font-size: $font-size-xxl;
      font-weight: $font-weight-semibold;
      color: $text-primary;
    }

    .stat-label {
      font-size: $font-size-sm;
      color: $text-muted;
    }
  }

  .stat-divider {
    @include divider;
    height: 30px;
  }
}

.main-actions {
  padding: 0 $spacing-md $spacing-base;
  display: flex;
  gap: $spacing-base;

  .action-button {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl $spacing-md;
    border-radius: $border-radius-base;
    gap: $spacing-sm;

    .button-icon {
      font-size: 28px;
      margin-bottom: $spacing-xs;
    }

    .button-text {
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
    }
  }

  .primary-button {
    @include button-primary;
    padding: $spacing-xl $spacing-md;
    flex-direction: column;
    height: auto;

    &:active {
      background-color: $primary-dark;
    }
  }

  .secondary-button {
    @include button-secondary;
    padding: $spacing-xl $spacing-md;
    flex-direction: column;
    height: auto;

    &:active {
      background-color: $background-color;
    }
  }
}

.function-list {
  @include card;
  margin-bottom: $spacing-base;

  .function-item {
    @include list-item;

    .function-icon {
      @include icon-container;
      margin-right: $spacing-base;
      font-size: 20px;
    }

    .function-content {
      flex: 1;

      .function-title {
        font-size: $font-size-md;
        color: $text-primary;
        margin-bottom: 2px;
      }

      .function-desc {
        font-size: $font-size-sm;
        color: $text-muted;
      }
    }

    .arrow-right {
      color: $text-muted;
      font-size: $font-size-sm;
    }
  }
}

.rating-section {
  text-align: center;
  padding: $spacing-lg;
  color: $text-muted;

  .rating-text {
    font-size: $font-size-sm;
  }
}

.active-room-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $primary-color;
  color: $text-white;
  padding: $spacing-md;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: $box-shadow-lg;

  .room-info {
    flex: 1;

    .room-status-badge {
      display: inline-block;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 2px 6px;
      border-radius: 10px;
      font-size: $font-size-xs;
      margin-bottom: $spacing-xs;
    }

    .room-text {
      font-size: $font-size-base;
      margin-bottom: 2px;

      .room-name {
        font-weight: $font-weight-semibold;
      }
    }

    .room-time {
      font-size: $font-size-xs;
      opacity: 0.8;
    }
  }

  .enter-room-btn {
    @include button-base;
    background-color: $text-white;
    color: $primary-color;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }
}

.game-style-modal {
  @include modal-overlay;

  .modal-content {
    @include modal-content;

    .modal-header {
      @include modal-header;
    }

    .game-style-options {
      padding: $spacing-lg;
      display: flex;
      gap: $spacing-md;

      .game-style-option {
        flex: 1;
        background-color: $background-color;
        border-radius: $border-radius-base;
        padding: $spacing-lg;
        text-align: center;
        border: 2px solid transparent;

        &:active {
          border-color: $primary-color;
        }

        .option-icon {
          font-size: 32px;
          margin-bottom: $spacing-base;
          .classic-icon,
          .score-icon {
            font-size: 40px;
          }
        }

        .option-title {
          font-size: $font-size-md;
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: $spacing-xs;
        }

        .option-description {
          font-size: $font-size-sm;
          color: $text-muted;
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
