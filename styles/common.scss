// 图标字体定义
// ==============================================
@font-face {
  font-family: "iconfont";
  src: url("https://env-00jxtnwq7mkk.normal.cloudstatic.cn/iconfont/iconfont.woff2?expire_at=1749021908&er_sign=ebdb5edd33c30da584374db4868c918f")
      format("woff2"),
    url("https://env-00jxtnwq7mkk.normal.cloudstatic.cn/iconfont/iconfont.woff?expire_at=1749021966&er_sign=c40fdcd8561fc6a364777a035d54904b")
      format("woff"),
    url("https://env-00jxtnwq7mkk.normal.cloudstatic.cn/iconfont/iconfont.ttf?expire_at=1749021940&er_sign=914cac32a7976884f541ad73183f7519")
      format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 微信风格设计规范变量
// ==============================================

// 主要颜色
$primary-color: #07c160; // 微信绿
$primary-light: #e8f5e8;
$primary-dark: #06ad56;
$secondary-color: #576b95; // 微信蓝
$accent-color: #fa5151; // 强调色/错误色
$warning-color: #ff976a; // 警告色
$success-color: #07c160; // 成功色（UI通用）
$info-color: #3b82f6; // 信息色

// 游戏业务专用颜色（红色=赢，绿色=输）
$win-color: #fa5151; // 红色 - 表示赢/正收益
$lose-color: #07c160; // 绿色 - 表示输/负收益
$positive-amount-color: #fa5151; // 正数金额颜色
$negative-amount-color: #07c160; // 负数金额颜色

// 背景颜色
$background-color: #f7f7f7; // 微信背景色
$card-background: #ffffff;
$overlay-background: rgba(0, 0, 0, 0.5);

// 文字颜色
$text-primary: #191919; // 主要文字
$text-secondary: #576b95; // 次要文字
$text-muted: #888888; // 辅助文字
$text-disabled: #c0c0c0; // 禁用文字
$text-white: #ffffff;

// 边框颜色
$border-color: #e5e5e5;
$border-light: #f0f0f0;
$border-dark: #d0d0d0;

// 字体大小规范
$font-size-xs: 11px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-huge: 28px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 间距规范
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 12px;
$spacing-md: 16px;
$spacing-lg: 20px;
$spacing-xl: 24px;
$spacing-xxl: 32px;

// 圆角规范
$border-radius-xs: 2px;
$border-radius-sm: 4px;
$border-radius-base: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-round: 50%;

// 阴影规范
$box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);

// 系统字体
$font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica,
  "Segoe UI", Arial, Roboto, "PingFang SC", "miui", "Hiragino Sans GB",
  "Microsoft Yahei", sans-serif;

// 基础样式重置
// ==============================================
page,
view,
text,
image,
button,
input,
textarea,
scroll-view,
swiper,
picker,
navigator,
audio,
video,
canvas,
map {
  box-sizing: border-box;
}
// 通用组件样式
// ==============================================

// 页面容器
@mixin page-container {
  min-height: 100vh;
  background-color: $background-color;
  color: $text-primary;
  font-family: $font-family;
  line-height: 1.5;
}

// 卡片样式
@mixin card {
  background-color: $card-background;
  border-radius: $border-radius-base;
  overflow: hidden;
}

@mixin card-shadow {
  @include card;
  box-shadow: $box-shadow-sm;
}

// 列表项样式
@mixin list-item {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: $background-color;
  }
}

// 按钮样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  user-select: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $primary-color;
  color: $text-white;

  &:active:not(:disabled) {
    background-color: $primary-dark;
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: $card-background;
  color: $text-primary;
  border: 1px solid $border-color;

  &:active:not(:disabled) {
    background-color: $background-color;
  }
}

@mixin button-text {
  @include button-base;
  background-color: transparent;
  color: $text-secondary;
  padding: $spacing-xs $spacing-sm;

  &:active:not(:disabled) {
    background-color: $background-color;
  }
}

// 输入框样式
@mixin input-base {
  width: 100%;
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  color: $text-primary;
  background-color: $card-background;
  outline: none;
  transition: border-color 0.2s ease;

  &::placeholder {
    color: $text-muted;
  }

  &:focus {
    border-color: $primary-color;
  }

  &:disabled {
    background-color: $background-color;
    color: $text-disabled;
    cursor: not-allowed;
  }
}

// 头像样式
@mixin avatar($size: 40px) {
  width: $size;
  height: $size;
  border-radius: $border-radius-sm;
  overflow: hidden;
  background-color: $background-color;
  display: flex;
  align-items: center;
  justify-content: center;

  // img,
  // image {
  //   width: 100%;
  //   height: 100%;
  //   object-fit: cover;
  // }
}

// 分割线
@mixin divider {
  width: 1px;
  background-color: $border-color;
}

@mixin divider-horizontal {
  height: 1px;
  background-color: $border-color;
}

// 图标容器
@mixin icon-container($size: 40px, $bg-color: $primary-light) {
  width: $size;
  height: $size;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $bg-color;
  border-radius: $border-radius-base;
}

// 弹窗样式
@mixin modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $overlay-background;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

@mixin modal-content {
  background-color: $card-background;
  width: 100%;
  border-top-left-radius: $border-radius-xl;
  border-top-right-radius: $border-radius-xl;
  overflow: hidden;
  max-height: 90vh;
}

@mixin modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $border-color;

  .modal-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  .close-button {
    @include button-text;
    width: 32px;
    height: 32px;
    padding: 0;
    color: $text-muted;
  }
}

// 状态类样式
// ==============================================

.text-primary {
  color: $text-primary;
}
.text-secondary {
  color: $text-secondary;
}
.text-muted {
  color: $text-muted;
}
.text-success {
  color: $success-color;
}
.text-warning {
  color: $warning-color;
}
.text-error {
  color: $accent-color;
}

// 游戏专用样式类
.text-win {
  color: $win-color;
}
.text-lose {
  color: $lose-color;
}
.amount-positive {
  color: $positive-amount-color;
}
.amount-negative {
  color: $negative-amount-color;
}

.bg-primary {
  background-color: $primary-color;
}
.bg-card {
  background-color: $card-background;
}
.bg-page {
  background-color: $background-color;
}

// 通用组件类
// ==============================================

.wechat-page {
  @include page-container;
}

.wechat-card {
  @include card;
}

.wechat-list-item {
  @include list-item;
}

.wechat-button {
  &--primary {
    @include button-primary;
  }

  &--secondary {
    @include button-secondary;
  }

  &--text {
    @include button-text;
  }
}

.wechat-input {
  @include input-base;
}

.wechat-avatar {
  @include avatar;

  &--sm {
    @include avatar(32px);
  }
  &--md {
    @include avatar(48px);
  }
  &--lg {
    @include avatar(64px);
  }
  &--xl {
    @include avatar(80px);
  }
}

.wechat-modal {
  &__overlay {
    @include modal-overlay;
  }

  &__content {
    @include modal-content;
  }

  &__header {
    @include modal-header;
  }
}

// 响应式辅助类
// ==============================================

.flex {
  display: flex;

  &--center {
    align-items: center;
    justify-content: center;
  }

  &--between {
    justify-content: space-between;
  }

  &--column {
    flex-direction: column;
  }
}

.mt-xs {
  margin-top: $spacing-xs;
}
.mt-sm {
  margin-top: $spacing-sm;
}
.mt-base {
  margin-top: $spacing-base;
}
.mt-md {
  margin-top: $spacing-md;
}
.mt-lg {
  margin-top: $spacing-lg;
}
.mt-xl {
  margin-top: $spacing-xl;
}

.mb-xs {
  margin-bottom: $spacing-xs;
}
.mb-sm {
  margin-bottom: $spacing-sm;
}
.mb-base {
  margin-bottom: $spacing-base;
}
.mb-md {
  margin-bottom: $spacing-md;
}
.mb-lg {
  margin-bottom: $spacing-lg;
}
.mb-xl {
  margin-bottom: $spacing-xl;
}

.p-xs {
  padding: $spacing-xs;
}
.p-sm {
  padding: $spacing-sm;
}
.p-base {
  padding: $spacing-base;
}
.p-md {
  padding: $spacing-md;
}
.p-lg {
  padding: $spacing-lg;
}
.p-xl {
  padding: $spacing-xl;
}
