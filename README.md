# 随心记分器 (Suixin Scorer)

一个功能强大的棋牌游戏计分应用，支持多种棋牌游戏的实时计分和结算。

## 🎯 项目简介

随心记分器是一款专为棋牌游戏设计的智能计分应用，支持多人在线实时计分、自动结算、茶水费管理等功能。无论是麻将、斗地主等经典玩法，还是牛牛、金花等给分玩法，都能轻松应对。

## ✨ 主要特性

### 🎮 双模式计分系统
- **经典玩法**: 先有输赢再付账（适用于麻将、斗地主等）
- **给分玩法**: 先押分再有输赢（适用于牛牛、金花等）

### 🏠 房间管理
- 创建私人房间，邀请好友参与
- 扫码快速加入房间
- 支持2-8人同时游戏
- 房主权限管理

### ⚡ 实时功能
- WebSocket实时通信，零延迟同步
- 实时计分更新
- 即时消息推送
- 网络状态监控

### 💰 智能计分
- 单人/批量计分支持
- 自动生成结算报告
- 茶水费智能抽成
- 精确的数值计算（支持小数）

### 📊 数据管理
- 完整的历史记录
- 胜负统计分析
- 个人资料管理
- 多平台数据同步

## 🛠 技术架构

### 前端技术栈
- **框架**: uni-app + Vue 3
- **样式**: SCSS + 响应式设计
- **状态管理**: Vuex + Mixin架构
- **工具库**: dayjs、number-precision

### 后端技术栈
- **云服务**: uniCloud (阿里云)
- **数据库**: MongoDB (uniCloud数据库)
- **实时通信**: WebSocket
- **身份认证**: JWT + 微信授权

### 核心模块
```
📁 项目结构
├── 🎨 components/          # 组件库
│   ├── avatar-display.vue      # 头像显示
│   ├── score-modal.vue         # 计分弹窗
│   ├── profile-editor.vue      # 资料编辑
│   └── ...
├── 📱 pages/              # 页面目录
│   ├── index/                  # 首页
│   ├── room/                   # 房间页面
│   ├── history/                # 历史记录
│   └── ...
├── 🔧 utils/              # 工具函数
│   ├── mathUtils.js            # 数学计算
│   ├── dateUtils.js            # 时间处理
│   ├── settlementUtils.js      # 结算逻辑
│   └── ...
├── ☁️ uniCloud-alipay/    # 云服务
│   ├── cloudfunctions/         # 云函数
│   │   ├── room/              # 房间服务
│   │   ├── user/              # 用户服务
│   │   └── room-websocket/    # WebSocket服务
│   └── database/              # 数据库结构
└── 🎭 pages/room/mixins/  # 房间页面混入
    ├── scoringMixin.js         # 计分逻辑
    ├── websocketMixin.js       # WebSocket通信
    └── ...
```

## 🚀 快速开始

### 环境要求
- Node.js >= 14.0.0
- HBuilderX 或 VSCode + uni-app插件
- 微信开发者工具（调试小程序）

### 安装依赖
```bash
# 使用 yarn 安装（推荐）
yarn install

# 或使用 npm
npm install
```

### 开发模式
```bash
# 微信小程序
npm run dev:mp-weixin

# 支付宝小程序  
npm run dev:mp-alipay

# H5
npm run dev:h5

# App
npm run dev:app
```

### 生产构建
```bash
# 构建微信小程序
npm run build:mp-weixin

# 构建支付宝小程序
npm run build:mp-alipay

# 构建H5
npm run build:h5
```

## 📖 使用指南

### 1. 创建房间
1. 点击首页"创建房间"按钮
2. 选择游戏玩法（经典/给分）
3. 等待好友加入房间

### 2. 加入房间
- **扫码加入**: 使用"扫码加入"功能扫描房间二维码
- **房间号**: 直接输入房间号加入

### 3. 开始计分
- **经典玩法**: 直接为玩家记分，支持正负分
- **给分玩法**: 分为"出分到桌面"和"收分"两个步骤

### 4. 茶水费设置
- 房主可设置茶水抽成比例（0-10%）
- 可设置茶水费上限
- 自动从每笔交易中抽取

### 5. 结算功能
- 自动生成详细结算报告
- 显示每位玩家的输赢情况
- 支持一键分享结算结果

## 🎮 功能详解

### 计分系统
- **精确计算**: 使用 number-precision 库避免浮点数精度问题
- **实时同步**: WebSocket确保所有玩家数据实时一致
- **撤销功能**: 支持撤销错误操作（管理员权限）

### 房间管理
- **生命周期**: 创建 → 进行中 → 结束
- **权限控制**: 房主拥有管理权限
- **自动清理**: 空房间自动结束，节省资源

### 数据安全
- **JWT认证**: 确保用户身份安全
- **权限验证**: 严格的数据访问控制
- **数据备份**: 云端自动备份，防止数据丢失


### 云服务配置
1. 在uniCloud控制台创建云空间
2. 关联项目到云空间
3. 上传并部署云函数
4. 初始化数据库结构

**随心记分器** - 让棋牌游戏更简单，让记分更精准！ 🎲✨