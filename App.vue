<script>
export default {
  onLaunch: function () {
    console.log("App Launch");
  },
  onShow: function () {
    console.log("App Show");
  },
  onHide: function () {
    console.log("App Hide");
  },
};
</script>

<style lang="scss">
@import "@/styles/common.scss";

/* 全局样式重置 */
page {
  background-color: $background-color;
  color: $text-primary;
  font-family: $font-family;
  line-height: 1.5;
}

/* 微信小程序特定样式 */
.uni-input-input {
  font-family: $font-family !important;
}

.uni-button {
  font-family: $font-family !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 通用动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter, .slide-up-leave-to {
  transform: translateY(100%);
}
</style>
